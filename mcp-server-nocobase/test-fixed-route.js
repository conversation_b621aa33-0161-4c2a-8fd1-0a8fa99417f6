#!/usr/bin/env node

/**
 * 测试修复后的路由创建功能
 */

import { spawn } from 'child_process';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// 简单的 MCP 客户端实现
class SimpleMCPClient {
  constructor() {
    this.serverProcess = null;
    this.requestId = 1;
  }

  async startServer() {
    console.log('🚀 启动 MCP 服务器...');
    
    this.serverProcess = spawn('node', [
      path.join(__dirname, 'dist', 'index.js'),
      '--base-url', 'https://app.dev.orb.local/api',
      '--token', 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VySWQiOjEsInJvbGVOYW1lIjoiYWRtaW4iLCJpYXQiOjE3NTQ1MjY4NDAsImV4cCI6MzMzMTIxMjY4NDB9.jub9QcFtDdjFrn3UzrT6PxNA19ZuHWGqIHfcBjdNhQA',
      '--app', 'mcp_playground'
    ], {
      stdio: ['pipe', 'pipe', 'pipe']
    });

    // 等待服务器启动
    await new Promise(resolve => setTimeout(resolve, 2000));
    console.log('✅ MCP 服务器已启动');
  }

  async sendRequest(method, params = {}) {
    return new Promise((resolve, reject) => {
      if (!this.serverProcess) {
        reject(new Error('Server not started'));
        return;
      }

      const request = {
        jsonrpc: '2.0',
        id: this.requestId++,
        method: method,
        params: params
      };

      let responseData = '';
      const timeout = setTimeout(() => {
        reject(new Error('Request timeout'));
      }, 30000);

      const onData = (data) => {
        responseData += data.toString();
        
        const lines = responseData.trim().split('\n');
        for (const line of lines) {
          if (line.trim().startsWith('{')) {
            try {
              const response = JSON.parse(line);
              if (response.id === request.id) {
                clearTimeout(timeout);
                this.serverProcess.stdout.off('data', onData);
                
                if (response.error) {
                  reject(new Error(response.error.message || 'MCP Error'));
                } else {
                  resolve(response.result);
                }
                return;
              }
            } catch (e) {
              // 继续等待完整的响应
            }
          }
        }
      };

      this.serverProcess.stdout.on('data', onData);
      this.serverProcess.stdin.write(JSON.stringify(request) + '\n');
    });
  }

  async callTool(toolName, args = {}) {
    return this.sendRequest('tools/call', {
      name: toolName,
      arguments: args
    });
  }

  async close() {
    if (this.serverProcess) {
      this.serverProcess.kill();
      this.serverProcess = null;
    }
  }
}

// 测试修复后的路由
async function testFixedRoute() {
  const client = new SimpleMCPClient();
  
  try {
    await client.startServer();
    
    // 初始化连接
    await client.sendRequest('initialize', {
      protocolVersion: '2024-11-05',
      capabilities: {},
      clientInfo: { name: 'fixed-route-test', version: '1.0.0' }
    });
    
    console.log('\n🛠️  === 测试修复后的路由创建功能 ===\n');
    
    // 步骤1: 创建修复后的商品管理页面
    console.log('📄 步骤1: 创建修复后的商品管理页面');
    let routeResult = null;
    
    try {
      routeResult = await client.callTool('create_page_route', {
        title: '商品管理（修复版）',
        icon: 'ShoppingCartOutlined',
        template: 'blank'
      });
      console.log('✅ 修复后的路由创建成功');
      console.log(routeResult.content[0].text);
    } catch (error) {
      console.log('❌ 创建路由失败:', error.message);
      return;
    }
    
    // 步骤2: 查看所有路由，确认新路由结构
    console.log('\n📋 步骤2: 查看所有路由');
    try {
      const allRoutes = await client.callTool('list_routes');
      console.log('✅ 路由列表:');
      console.log(allRoutes.content[0].text);
    } catch (error) {
      console.log('❌ 获取路由列表失败:', error.message);
    }
    
    // 步骤3: 提取新创建的路由信息
    console.log('\n🔍 步骤3: 分析新创建的路由结构');
    
    // 从返回结果中提取路由信息
    const routeText = routeResult.content[0].text;
    let mainRouteId = null;
    let pageUid = null;
    let tabUid = null;
    
    try {
      // 提取主路由 ID
      const mainRouteMatch = routeText.match(/"id":\s*(\d+)/);
      if (mainRouteMatch) {
        mainRouteId = mainRouteMatch[1];
        console.log(`✅ 主路由 ID: ${mainRouteId}`);
      }
      
      // 提取页面 UID
      const pageUidMatch = routeText.match(/Page UID: ([a-zA-Z0-9-]+)/);
      if (pageUidMatch) {
        pageUid = pageUidMatch[1];
        console.log(`✅ 页面 UID: ${pageUid}`);
      }
      
      // 提取标签页 UID
      const tabUidMatch = routeText.match(/Tab UID: ([a-zA-Z0-9-]+)/);
      if (tabUidMatch) {
        tabUid = tabUidMatch[1];
        console.log(`✅ 标签页 UID: ${tabUid}`);
      }
    } catch (error) {
      console.log('⚠️  解析路由信息时出错:', error.message);
    }
    
    // 步骤4: 检查页面 Schema
    if (pageUid) {
      console.log('\n🏗️  步骤4: 检查页面 Schema');
      try {
        const pageSchema = await client.callTool('get_page_schema', {
          schemaUid: pageUid
        });
        console.log('✅ 页面 Schema:');
        console.log(pageSchema.content[0].text);
      } catch (error) {
        console.log('❌ 获取页面 Schema 失败:', error.message);
      }
    }
    
    // 步骤5: 检查标签页 Schema
    if (tabUid) {
      console.log('\n📑 步骤5: 检查标签页 Schema');
      try {
        const tabSchema = await client.callTool('get_page_schema', {
          schemaUid: tabUid
        });
        console.log('✅ 标签页 Schema:');
        console.log(tabSchema.content[0].text);
      } catch (error) {
        console.log('❌ 获取标签页 Schema 失败:', error.message);
      }
    }
    
    // 步骤6: 尝试添加表格区块到新页面
    if (tabUid) {
      console.log('\n📊 步骤6: 尝试添加商品表格区块');
      
      // 查找商品集合
      try {
        const collections = await client.callTool('list_collections');
        const collectionsText = collections.content[0].text;
        
        const productMatches = collectionsText.match(/• (products_\d+) \(/g);
        if (productMatches && productMatches.length > 0) {
          const productCollection = productMatches[productMatches.length - 1].match(/products_\d+/)[0];
          console.log(`✅ 找到商品集合: ${productCollection}`);
          
          // 尝试添加表格区块
          try {
            const tableBlockResult = await client.callTool('add_table_block', {
              parentUid: tabUid,  // 使用标签页 UID 作为父容器
              collectionName: productCollection,
              title: '商品列表',
              showActions: true,
              showPagination: true,
              pageSize: 20
            });
            console.log('✅ 表格区块添加成功:');
            console.log(tableBlockResult.content[0].text);
          } catch (blockError) {
            console.log('❌ 添加表格区块失败:', blockError.message);
          }
        } else {
          console.log('❌ 未找到商品集合');
        }
      } catch (error) {
        console.log('❌ 查找商品集合失败:', error.message);
      }
    }
    
    console.log('\n🎉 修复后的路由测试完成！');
    console.log('\n📝 测试总结:');
    console.log('✅ 创建了包含子路由的页面');
    console.log('✅ 页面结构模仿 courses 路由');
    console.log('✅ 包含 tabs 类型的子路由');
    console.log('✅ 子路由包含 Grid 和 page:addBlock 初始化器');
    
    console.log('\n💡 下一步:');
    console.log('1. 在 NocoBase 管理界面中查看新创建的"商品管理（修复版）"页面');
    console.log('2. 验证页面是否可以正常添加区块');
    console.log('3. 测试表格区块是否正确显示商品数据');
    
  } catch (error) {
    console.error('❌ 测试失败:', error.message);
  } finally {
    await client.close();
    console.log('\n🔚 MCP 服务器已关闭');
  }
}

// 运行测试
if (import.meta.url === `file://${process.argv[1]}`) {
  testFixedRoute().catch(console.error);
}
