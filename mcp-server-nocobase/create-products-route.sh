#!/bin/bash

# 商品页面路由创建执行脚本
# 自动化创建NocoBase商品管理页面路由

set -e  # 遇到错误立即退出

echo "🚀 NocoBase 商品页面路由创建工具"
echo "=================================="

# 检查当前目录
if [ ! -f "package.json" ]; then
    echo "❌ 错误: 请在mcp-server-nocobase目录中运行此脚本"
    exit 1
fi

# 检查Node.js
if ! command -v node &> /dev/null; then
    echo "❌ 错误: Node.js未安装"
    exit 1
fi

echo "✅ 环境检查通过"

# 构建项目
echo "📦 构建MCP服务器..."
npm run build

if [ $? -ne 0 ]; then
    echo "❌ 构建失败"
    exit 1
fi

echo "✅ 构建完成"

# 创建配置目录
mkdir -p config

# 检查脚本文件
SCRIPTS=("create-products-route.js" "create-route-simple.js")
for script in "${SCRIPTS[@]}"; do
    if [ ! -f "$script" ]; then
        echo "❌ 错误: 脚本文件 $script 不存在"
        exit 1
    fi
done

echo "✅ 脚本文件检查通过"

# 询问用户选择
echo ""
echo "请选择创建方式:"
echo "1) 完整创建 (推荐) - 包含详细日志和错误处理"
echo "2) 简化创建 - 快速创建，简洁输出"
echo "3) 仅显示配置信息"
echo ""

read -p "请输入选择 (1-3): " choice

case $choice in
    1)
        echo ""
        echo "🛍️ 开始完整创建商品页面路由..."
        echo "=================================="
        node create-products-route.js
        ;;
    2)
        echo ""
        echo "🛍️ 开始简化创建商品页面路由..."
        echo "=================================="
        node create-route-simple.js
        ;;
    3)
        echo ""
        echo "📋 路由配置信息"
        echo "================"
        echo "页面标题: 商品管理"
        echo "页面图标: ShoppingCartOutlined"
        echo "页面模板: table"
        echo "数据集合: products"
        echo "Tabs功能: 启用"
        echo "隐藏状态: 否"
        echo ""
        echo "📝 预期字段:"
        echo "- id (主键)"
        echo "- name (商品名称)"
        echo "- price (价格)"
        echo "- description (描述)"
        echo "- category (分类)"
        echo "- stock (库存)"
        echo "- status (状态)"
        echo "- createdAt (创建时间)"
        echo "- updatedAt (更新时间)"
        echo "- createdBy (创建人)"
        echo "- updatedBy (更新人)"
        ;;
    *)
        echo "❌ 无效选择"
        exit 1
        ;;
esac

# 检查执行结果
if [ $? -eq 0 ]; then
    echo ""
    echo "🎉 创建完成!"
    echo "============"
    echo "💡 请在NocoBase管理界面中查看新创建的'商品管理'页面"
    echo "💡 如果页面显示空白，请确保products集合存在"
    echo "💡 如有问题，请查看日志或参考README文档"
    echo ""
    echo "📚 相关文档:"
    echo "- CREATE_PRODUCTS_ROUTE.md - 详细说明"
    echo "- README_PRODUCTS_ROUTE.md - 完整指南"
    echo "- config/products-route-config.js - 配置文件"
else
    echo ""
    echo "❌ 创建失败"
    echo "=========="
    echo "💡 请检查以下事项:"
    echo "1. NocoBase服务器是否正常运行"
    echo "2. API token是否有效"
    echo "3. 网络连接是否正常"
    echo "4. 是否有创建页面的权限"
    echo ""
    echo "📋 调试信息:"
    echo "- 查看控制台输出获取详细错误信息"
    echo "- 检查MCP服务器日志"
    echo "- 验证API连接状态"
    exit 1
fi

echo ""
echo "🔚 脚本执行完成"