#!/usr/bin/env node

/**
 * 检查 Schema API - 验证创建的页面结构
 */

import { spawn } from 'child_process';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// 简单的 MCP 客户端实现
class SimpleMCPClient {
  constructor() {
    this.serverProcess = null;
    this.requestId = 1;
  }

  async startServer() {
    console.log('🚀 启动 MCP 服务器...');
    
    this.serverProcess = spawn('node', [
      path.join(__dirname, 'dist', 'index.js'),
      '--base-url', 'https://app.dev.orb.local/api',
      '--token', 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VySWQiOjEsInJvbGVOYW1lIjoiYWRtaW4iLCJpYXQiOjE3NTQ1MjY4NDAsImV4cCI6MzMzMTIxMjY4NDB9.jub9QcFtDdjFrn3UzrT6PxNA19ZuHWGqIHfcBjdNhQA',
      '--app', 'mcp_playground'
    ], {
      stdio: ['pipe', 'pipe', 'pipe']
    });

    // 等待服务器启动
    await new Promise(resolve => setTimeout(resolve, 2000));
    console.log('✅ MCP 服务器已启动');
  }

  async sendRequest(method, params = {}) {
    return new Promise((resolve, reject) => {
      if (!this.serverProcess) {
        reject(new Error('Server not started'));
        return;
      }

      const request = {
        jsonrpc: '2.0',
        id: this.requestId++,
        method: method,
        params: params
      };

      let responseData = '';
      const timeout = setTimeout(() => {
        reject(new Error('Request timeout'));
      }, 30000);

      const onData = (data) => {
        responseData += data.toString();
        
        const lines = responseData.trim().split('\n');
        for (const line of lines) {
          if (line.trim().startsWith('{')) {
            try {
              const response = JSON.parse(line);
              if (response.id === request.id) {
                clearTimeout(timeout);
                this.serverProcess.stdout.off('data', onData);
                
                if (response.error) {
                  reject(new Error(response.error.message || 'MCP Error'));
                } else {
                  resolve(response.result);
                }
                return;
              }
            } catch (e) {
              // 继续等待完整的响应
            }
          }
        }
      };

      this.serverProcess.stdout.on('data', onData);
      this.serverProcess.stdin.write(JSON.stringify(request) + '\n');
    });
  }

  async callTool(toolName, args = {}) {
    return this.sendRequest('tools/call', {
      name: toolName,
      arguments: args
    });
  }

  async close() {
    if (this.serverProcess) {
      this.serverProcess.kill();
      this.serverProcess = null;
    }
  }
}

// 检查 Schema API
async function checkSchemaAPI() {
  const client = new SimpleMCPClient();
  
  try {
    await client.startServer();
    
    // 初始化连接
    await client.sendRequest('initialize', {
      protocolVersion: '2024-11-05',
      capabilities: {},
      clientInfo: { name: 'schema-check-client', version: '1.0.0' }
    });
    
    console.log('\n🔍 === 检查 Schema API 和页面结构 ===\n');
    
    // 步骤1: 列出所有路由
    console.log('📋 步骤1: 列出所有路由');
    let routes = null;
    try {
      const routesResult = await client.callTool('list_routes');
      routes = routesResult.content[0].text;
      console.log('✅ 路由列表:');
      console.log(routes);
    } catch (error) {
      console.log('❌ 获取路由失败:', error.message);
      return;
    }
    
    // 步骤2: 提取页面 UID
    console.log('\n🔍 步骤2: 提取页面 UID');
    const pageUids = [];
    
    // 从路由信息中提取页面 UID
    const uidMatches = routes.match(/page-\d+-[a-zA-Z0-9]+/g);
    if (uidMatches) {
      pageUids.push(...uidMatches);
      console.log('✅ 找到页面 UID:', pageUids);
    } else {
      console.log('❌ 未找到页面 UID');
      return;
    }
    
    // 步骤3: 检查每个页面的 Schema
    console.log('\n🏗️  步骤3: 检查页面 Schema');
    
    for (let i = 0; i < pageUids.length; i++) {
      const pageUid = pageUids[i];
      console.log(`\n--- 页面 ${i + 1}: ${pageUid} ---`);
      
      try {
        const schemaResult = await client.callTool('get_page_schema', {
          schemaUid: pageUid
        });
        console.log('✅ 页面 Schema:');
        console.log(schemaResult.content[0].text);
      } catch (error) {
        console.log('❌ 获取页面 Schema 失败:', error.message);
        
        // 尝试使用其他方法获取页面信息
        try {
          const blocksResult = await client.callTool('list_page_blocks', {
            schemaUid: pageUid
          });
          console.log('✅ 页面区块:');
          console.log(blocksResult.content[0].text);
        } catch (blockError) {
          console.log('❌ 获取页面区块也失败:', blockError.message);
        }
      }
    }
    
    // 步骤4: 检查商品集合
    console.log('\n📦 步骤4: 检查商品集合');
    try {
      const collections = await client.callTool('list_collections');
      const collectionsText = collections.content[0].text;
      
      // 查找商品集合
      const productMatches = collectionsText.match(/• (products_\d+) \(/g);
      if (productMatches && productMatches.length > 0) {
        const productCollection = productMatches[productMatches.length - 1].match(/products_\d+/)[0];
        console.log(`✅ 找到商品集合: ${productCollection}`);
        
        // 检查商品集合的字段
        const fieldsResult = await client.callTool('list_fields', {
          collection: productCollection
        });
        console.log('✅ 商品集合字段:');
        console.log(fieldsResult.content[0].text);
        
        // 检查商品数据
        const recordsResult = await client.callTool('list_records', {
          collection: productCollection
        });
        console.log('✅ 商品数据:');
        console.log(recordsResult.content[0].text);
        
      } else {
        console.log('❌ 未找到商品集合');
      }
    } catch (error) {
      console.log('❌ 检查商品集合失败:', error.message);
    }
    
    // 步骤5: 检查可用的 MCP 工具
    console.log('\n🔧 步骤5: 检查可用的 MCP 工具');
    try {
      const tools = await client.sendRequest('tools/list');
      const toolNames = tools.tools?.map(t => t.name) || [];
      console.log(`✅ 共 ${toolNames.length} 个可用工具:`);
      
      // 按类别分组显示工具
      const categories = {
        '集合管理': toolNames.filter(name => name.includes('collection')),
        '字段管理': toolNames.filter(name => name.includes('field')),
        '记录操作': toolNames.filter(name => name.includes('record')),
        '路由页面': toolNames.filter(name => name.includes('route') || name.includes('page')),
        '区块管理': toolNames.filter(name => name.includes('block')),
        '其他工具': toolNames.filter(name => 
          !name.includes('collection') && 
          !name.includes('field') && 
          !name.includes('record') && 
          !name.includes('route') && 
          !name.includes('page') && 
          !name.includes('block')
        )
      };
      
      for (const [category, tools] of Object.entries(categories)) {
        if (tools.length > 0) {
          console.log(`  ${category}: ${tools.join(', ')}`);
        }
      }
    } catch (error) {
      console.log('❌ 获取工具列表失败:', error.message);
    }
    
    console.log('\n📝 检查总结:');
    console.log('✅ 成功创建了多个页面路由');
    console.log('✅ 页面 UID 提取成功');
    console.log('✅ 商品集合和数据完整');
    console.log('⚠️  页面内容为空（需要手动添加区块或修复区块添加功能）');
    
    console.log('\n💡 问题分析:');
    console.log('1. 路由创建成功 - MCP 工具正常工作');
    console.log('2. 页面为空白 - 区块添加可能有问题');
    console.log('3. 商品数据完整 - 默认字段功能正常');
    console.log('4. 建议手动在 NocoBase 界面中添加表格区块来显示商品数据');
    
  } catch (error) {
    console.error('❌ 检查失败:', error.message);
  } finally {
    await client.close();
    console.log('\n🔚 MCP 服务器已关闭');
  }
}

// 运行检查
if (import.meta.url === `file://${process.argv[1]}`) {
  checkSchemaAPI().catch(console.error);
}
