#!/usr/bin/env node

/**
 * 测试创建商品集合 - 验证默认字段自动添加功能
 */

import { spawn } from 'child_process';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// 简单的 MCP 客户端实现
class SimpleMCPClient {
  constructor() {
    this.serverProcess = null;
    this.requestId = 1;
  }

  async startServer() {
    console.log('🚀 启动 MCP 服务器...');
    
    this.serverProcess = spawn('node', [
      path.join(__dirname, 'dist', 'index.js'),
      '--base-url', 'https://app.dev.orb.local/api',
      '--token', 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VySWQiOjEsInJvbGVOYW1lIjoiYWRtaW4iLCJpYXQiOjE3NTQ1MjY4NDAsImV4cCI6MzMzMTIxMjY4NDB9.jub9QcFtDdjFrn3UzrT6PxNA19ZuHWGqIHfcBjdNhQA',
      '--app', 'mcp_playground'
    ], {
      stdio: ['pipe', 'pipe', 'pipe']
    });

    // 等待服务器启动
    await new Promise(resolve => setTimeout(resolve, 2000));
    console.log('✅ MCP 服务器已启动');
  }

  async sendRequest(method, params = {}) {
    return new Promise((resolve, reject) => {
      if (!this.serverProcess) {
        reject(new Error('Server not started'));
        return;
      }

      const request = {
        jsonrpc: '2.0',
        id: this.requestId++,
        method: method,
        params: params
      };

      let responseData = '';
      const timeout = setTimeout(() => {
        reject(new Error('Request timeout'));
      }, 30000);

      const onData = (data) => {
        responseData += data.toString();
        
        const lines = responseData.trim().split('\n');
        for (const line of lines) {
          if (line.trim().startsWith('{')) {
            try {
              const response = JSON.parse(line);
              if (response.id === request.id) {
                clearTimeout(timeout);
                this.serverProcess.stdout.off('data', onData);
                
                if (response.error) {
                  reject(new Error(response.error.message || 'MCP Error'));
                } else {
                  resolve(response.result);
                }
                return;
              }
            } catch (e) {
              // 继续等待完整的响应
            }
          }
        }
      };

      this.serverProcess.stdout.on('data', onData);
      this.serverProcess.stdin.write(JSON.stringify(request) + '\n');
    });
  }

  async callTool(toolName, args = {}) {
    return this.sendRequest('tools/call', {
      name: toolName,
      arguments: args
    });
  }

  async close() {
    if (this.serverProcess) {
      this.serverProcess.kill();
      this.serverProcess = null;
    }
  }
}

// 测试商品集合创建
async function testProductsCollection() {
  const client = new SimpleMCPClient();
  
  try {
    await client.startServer();
    
    // 初始化连接
    await client.sendRequest('initialize', {
      protocolVersion: '2024-11-05',
      capabilities: {},
      clientInfo: { name: 'products-test-client', version: '1.0.0' }
    });
    
    console.log('\n🛍️  === 创建商品集合测试 ===\n');
    
    const timestamp = Date.now();
    const collectionName = `products_${timestamp}`;
    
    // 步骤1: 创建商品集合（包含所有默认字段）
    console.log('📦 步骤1: 创建商品集合');
    console.log(`   集合名称: ${collectionName}`);
    console.log('   包含默认字段: ID、创建时间、更新时间、创建人、更新人');
    
    try {
      const createResult = await client.callTool('create_collection', {
        name: collectionName,
        title: '商品管理',
        description: '商品信息管理集合',
        autoGenId: true,      // 自动生成ID
        createdAt: true,      // 创建时间
        updatedAt: true,      // 更新时间
        createdBy: true,      // 创建人
        updatedBy: true       // 更新人
      });
      console.log('✅', createResult.content[0].text);
    } catch (error) {
      console.log('❌ 创建集合失败:', error.message);
      return;
    }
    
    // 步骤2: 查看自动创建的默认字段
    console.log('\n🔍 步骤2: 查看自动创建的默认字段');
    try {
      const fieldsResult = await client.callTool('list_fields', {
        collection: collectionName
      });
      console.log('✅ 默认字段列表:');
      console.log(fieldsResult.content[0].text);
    } catch (error) {
      console.log('❌ 查看字段失败:', error.message);
    }
    
    // 步骤3: 添加商品特有的业务字段
    console.log('\n➕ 步骤3: 添加商品业务字段');
    
    const businessFields = [
      {
        name: 'name',
        type: 'string',
        interface: 'input',
        title: '商品名称',
        required: true
      },
      {
        name: 'price',
        type: 'decimal',
        interface: 'number',
        title: '价格',
        precision: 10,
        scale: 2
      },
      {
        name: 'description',
        type: 'text',
        interface: 'textarea',
        title: '商品描述'
      },
      {
        name: 'category',
        type: 'string',
        interface: 'select',
        title: '商品分类',
        options: [
          { label: '电子产品', value: 'electronics' },
          { label: '服装', value: 'clothing' },
          { label: '食品', value: 'food' },
          { label: '图书', value: 'books' }
        ]
      },
      {
        name: 'stock',
        type: 'integer',
        interface: 'number',
        title: '库存数量'
      },
      {
        name: 'status',
        type: 'string',
        interface: 'select',
        title: '商品状态',
        options: [
          { label: '上架', value: 'active' },
          { label: '下架', value: 'inactive' },
          { label: '缺货', value: 'out_of_stock' }
        ]
      }
    ];
    
    for (const field of businessFields) {
      try {
        const fieldResult = await client.callTool('create_field', {
          collection: collectionName,
          name: field.name,
          type: field.type,
          interface: field.interface,
          uiSchema: {
            title: field.title,
            'x-component': field.interface === 'input' ? 'Input' :
                          field.interface === 'number' ? 'InputNumber' :
                          field.interface === 'textarea' ? 'Input.TextArea' :
                          field.interface === 'select' ? 'Select' : 'Input',
            'x-component-props': field.options ? { options: field.options } : {},
            required: field.required || false
          },
          ...(field.precision && { precision: field.precision }),
          ...(field.scale && { scale: field.scale })
        });
        console.log(`✅ 添加字段 ${field.name} (${field.title}) 成功`);
      } catch (error) {
        console.log(`❌ 添加字段 ${field.name} 失败:`, error.message);
      }
    }
    
    // 步骤4: 查看完整的字段列表
    console.log('\n📋 步骤4: 查看完整的字段列表');
    try {
      const allFieldsResult = await client.callTool('list_fields', {
        collection: collectionName
      });
      console.log('✅ 完整字段列表（默认字段 + 业务字段）:');
      console.log(allFieldsResult.content[0].text);
    } catch (error) {
      console.log('❌ 查看完整字段失败:', error.message);
    }
    
    // 步骤5: 创建测试商品记录
    console.log('\n🛒 步骤5: 创建测试商品记录');
    
    const testProducts = [
      {
        name: 'iPhone 15 Pro',
        price: 7999.00,
        description: '苹果最新款智能手机，配备A17 Pro芯片',
        category: 'electronics',
        stock: 50,
        status: 'active'
      },
      {
        name: '纯棉T恤',
        price: 89.90,
        description: '100%纯棉材质，舒适透气',
        category: 'clothing',
        stock: 200,
        status: 'active'
      },
      {
        name: '有机苹果',
        price: 12.50,
        description: '新鲜有机苹果，产地山东',
        category: 'food',
        stock: 0,
        status: 'out_of_stock'
      }
    ];
    
    for (const product of testProducts) {
      try {
        const recordResult = await client.callTool('create_record', {
          collection: collectionName,
          data: product
        });
        console.log(`✅ 创建商品 "${product.name}" 成功`);
      } catch (error) {
        console.log(`❌ 创建商品 "${product.name}" 失败:`, error.message);
      }
    }
    
    // 步骤6: 查看创建的商品记录（验证默认字段自动填充）
    console.log('\n📊 步骤6: 查看商品记录（验证默认字段）');
    try {
      const recordsResult = await client.callTool('list_records', {
        collection: collectionName
      });
      console.log('✅ 商品记录列表（注意默认字段的自动填充）:');
      console.log(recordsResult.content[0].text);
    } catch (error) {
      console.log('❌ 查看商品记录失败:', error.message);
    }
    
    console.log('\n🎉 商品集合创建测试完成！');
    console.log('\n📝 测试总结:');
    console.log('✅ 成功创建商品集合，包含所有默认字段');
    console.log('✅ 默认字段自动添加：ID、创建时间、更新时间、创建人、更新人');
    console.log('✅ 成功添加6个业务字段：名称、价格、描述、分类、库存、状态');
    console.log('✅ 成功创建3个测试商品记录');
    console.log('✅ 验证了默认字段在记录创建时自动填充');
    console.log(`\n🔗 集合名称: ${collectionName}`);
    console.log('💡 您可以在 NocoBase 管理界面中查看这个集合和数据');
    
  } catch (error) {
    console.error('❌ 测试失败:', error.message);
  } finally {
    await client.close();
    console.log('\n🔚 MCP 服务器已关闭');
  }
}

// 运行测试
if (import.meta.url === `file://${process.argv[1]}`) {
  testProductsCollection().catch(console.error);
}
