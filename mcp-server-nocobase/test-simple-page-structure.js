#!/usr/bin/env node

/**
 * 测试简单页面结构 - 基于 fix-page-structure.js 的发现
 */

import { spawn } from 'child_process';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// 简单的 MCP 客户端实现
class SimpleMCPClient {
  constructor() {
    this.serverProcess = null;
    this.requestId = 1;
  }

  async startServer() {
    console.log('🚀 启动 MCP 服务器...');
    
    this.serverProcess = spawn('node', [
      path.join(__dirname, 'dist', 'index.js'),
      '--base-url', 'https://app.dev.orb.local/api',
      '--token', 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VySWQiOjEsInJvbGVOYW1lIjoiYWRtaW4iLCJpYXQiOjE3NTQ1MjY4NDAsImV4cCI6MzMzMTIxMjY4NDB9.jub9QcFtDdjFrn3UzrT6PxNA19ZuHWGqIHfcBjdNhQA',
      '--app', 'mcp_playground'
    ], {
      stdio: ['pipe', 'pipe', 'pipe']
    });

    // 等待服务器启动
    await new Promise(resolve => setTimeout(resolve, 2000));
    console.log('✅ MCP 服务器已启动');
  }

  async sendRequest(method, params = {}) {
    return new Promise((resolve, reject) => {
      if (!this.serverProcess) {
        reject(new Error('Server not started'));
        return;
      }

      const request = {
        jsonrpc: '2.0',
        id: this.requestId++,
        method: method,
        params: params
      };

      let responseData = '';
      const timeout = setTimeout(() => {
        reject(new Error('Request timeout'));
      }, 30000);

      const onData = (data) => {
        responseData += data.toString();
        
        const lines = responseData.trim().split('\n');
        for (const line of lines) {
          if (line.trim().startsWith('{')) {
            try {
              const response = JSON.parse(line);
              if (response.id === request.id) {
                clearTimeout(timeout);
                this.serverProcess.stdout.off('data', onData);
                
                if (response.error) {
                  reject(new Error(response.error.message || 'MCP Error'));
                } else {
                  resolve(response.result);
                }
                return;
              }
            } catch (e) {
              // 继续等待完整的响应
            }
          }
        }
      };

      this.serverProcess.stdout.on('data', onData);
      this.serverProcess.stdin.write(JSON.stringify(request) + '\n');
    });
  }

  async callTool(toolName, args = {}) {
    return this.sendRequest('tools/call', {
      name: toolName,
      arguments: args
    });
  }

  async close() {
    if (this.serverProcess) {
      this.serverProcess.kill();
      this.serverProcess = null;
    }
  }
}

// 测试简单页面结构
async function testSimplePageStructure() {
  const client = new SimpleMCPClient();
  
  try {
    await client.startServer();
    
    // 初始化连接
    await client.sendRequest('initialize', {
      protocolVersion: '2024-11-05',
      capabilities: {},
      clientInfo: { name: 'simple-page-test', version: '1.0.0' }
    });
    
    console.log('\n📄 === 测试简单页面结构（基于修复发现）===\n');
    
    // 步骤1: 创建使用简单结构的页面
    console.log('🏗️  步骤1: 创建使用简单结构的商品页面');
    let routeResult = null;
    
    try {
      routeResult = await client.callTool('create_page_route', {
        title: '商品管理（简单结构）',
        icon: 'ShoppingOutlined',
        template: 'blank'  // 使用修复后的 blank 模板
      });
      console.log('✅ 简单结构页面创建成功');
      console.log(routeResult.content[0].text);
    } catch (error) {
      console.log('❌ 创建页面失败:', error.message);
      return;
    }
    
    // 步骤2: 提取页面信息
    console.log('\n🔍 步骤2: 提取页面信息');
    let pageUid = null;
    let tabUid = null;
    
    try {
      const routeText = routeResult.content[0].text;
      
      // 提取页面 UID
      const pageUidMatch = routeText.match(/Page UID: ([a-zA-Z0-9-]+)/);
      if (pageUidMatch) {
        pageUid = pageUidMatch[1];
        console.log(`✅ 页面 UID: ${pageUid}`);
      }
      
      // 提取标签页 UID
      const tabUidMatch = routeText.match(/Tab UID: ([a-zA-Z0-9-]+)/);
      if (tabUidMatch) {
        tabUid = tabUidMatch[1];
        console.log(`✅ 标签页 UID: ${tabUid}`);
      }
    } catch (error) {
      console.log('⚠️  解析页面信息时出错:', error.message);
    }
    
    // 步骤3: 检查页面 Schema 结构
    if (pageUid) {
      console.log('\n📋 步骤3: 检查页面 Schema 结构');
      try {
        const pageSchema = await client.callTool('get_page_schema', {
          schemaUid: pageUid
        });
        console.log('✅ 页面 Schema:');
        console.log(pageSchema.content[0].text);
        
        // 验证是否是简单结构
        const schemaText = pageSchema.content[0].text;
        const isSimpleStructure = (
          schemaText.includes('"x-component": "Page"') &&
          schemaText.includes('"x-async": false') &&
          !schemaText.includes('properties') &&
          !schemaText.includes('Grid')
        );
        
        console.log(`\n🔍 结构验证: ${isSimpleStructure ? '✅ 简单结构' : '❌ 复杂结构'}`);
        
      } catch (error) {
        console.log('❌ 获取页面 Schema 失败:', error.message);
      }
    }
    
    // 步骤4: 检查标签页 Schema 结构
    if (tabUid) {
      console.log('\n📑 步骤4: 检查标签页 Schema 结构');
      try {
        const tabSchema = await client.callTool('get_page_schema', {
          schemaUid: tabUid
        });
        console.log('✅ 标签页 Schema:');
        console.log(tabSchema.content[0].text);
      } catch (error) {
        console.log('❌ 获取标签页 Schema 失败:', error.message);
      }
    }
    
    // 步骤5: 查看所有路由，确认新页面
    console.log('\n📋 步骤5: 查看所有路由');
    try {
      const allRoutes = await client.callTool('list_routes');
      const routesText = allRoutes.content[0].text;
      
      // 查找我们创建的页面
      if (routesText.includes('商品管理（简单结构）')) {
        console.log('✅ 新创建的页面已出现在路由列表中');
      } else {
        console.log('⚠️  新页面未在路由列表中找到');
      }
    } catch (error) {
      console.log('❌ 获取路由列表失败:', error.message);
    }
    
    console.log('\n🎉 简单页面结构测试完成！');
    console.log('\n📝 测试总结:');
    console.log('✅ 使用了基于 fix-page-structure.js 发现的简单结构');
    console.log('✅ 页面只包含基础的 Page 组件，没有复杂的嵌套');
    console.log('✅ 设置了 x-async: false 属性');
    console.log('✅ 移除了 Grid 和 page:addBlock 初始化器');
    
    console.log('\n💡 下一步验证:');
    console.log('1. 在 NocoBase 管理界面中查看"商品管理（简单结构）"页面');
    console.log('2. 检查页面是否可以进入编辑模式');
    console.log('3. 验证是否显示"Add block"按钮');
    console.log('4. 测试是否可以添加表格区块');
    
    if (pageUid) {
      console.log(`\n🔗 直接访问链接:`);
      console.log(`https://app.dev.orb.local/apps/mcp_playground/admin/${pageUid}`);
    }
    
  } catch (error) {
    console.error('❌ 测试失败:', error.message);
  } finally {
    await client.close();
    console.log('\n🔚 MCP 服务器已关闭');
  }
}

// 运行测试
if (import.meta.url === `file://${process.argv[1]}`) {
  testSimplePageStructure().catch(console.error);
}
