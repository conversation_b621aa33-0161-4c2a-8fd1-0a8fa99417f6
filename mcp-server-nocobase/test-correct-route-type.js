#!/usr/bin/env node

/**
 * 测试修正后的路由创建（使用正确的 "tab" 类型）
 * 基于 test_routes_api.http 的发现
 */

import { spawn } from 'child_process';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// 简单的 MCP 客户端实现
class SimpleMCPClient {
  constructor() {
    this.serverProcess = null;
    this.requestId = 1;
  }

  async startServer() {
    console.log('🚀 启动 MCP 服务器...');
    
    this.serverProcess = spawn('node', [
      path.join(__dirname, 'dist', 'index.js'),
      '--base-url', 'https://app.dev.orb.local/api',
      '--token', 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VySWQiOjEsInJvbGVOYW1lIjoiYWRtaW4iLCJpYXQiOjE3NTQ1MjY4NDAsImV4cCI6MzMzMTIxMjY4NDB9.jub9QcFtDdjFrn3UzrT6PxNA19ZuHWGqIHfcBjdNhQA',
      '--app', 'mcp_playground'
    ], {
      stdio: ['pipe', 'pipe', 'pipe']
    });

    // 等待服务器启动
    await new Promise(resolve => setTimeout(resolve, 2000));
    console.log('✅ MCP 服务器已启动');
  }

  async sendRequest(method, params = {}) {
    return new Promise((resolve, reject) => {
      if (!this.serverProcess) {
        reject(new Error('Server not started'));
        return;
      }

      const request = {
        jsonrpc: '2.0',
        id: this.requestId++,
        method: method,
        params: params
      };

      let responseData = '';
      const timeout = setTimeout(() => {
        reject(new Error('Request timeout'));
      }, 30000);

      const onData = (data) => {
        responseData += data.toString();
        
        const lines = responseData.trim().split('\n');
        for (const line of lines) {
          if (line.trim().startsWith('{')) {
            try {
              const response = JSON.parse(line);
              if (response.id === request.id) {
                clearTimeout(timeout);
                this.serverProcess.stdout.off('data', onData);
                
                if (response.error) {
                  reject(new Error(response.error.message || 'MCP Error'));
                } else {
                  resolve(response.result);
                }
                return;
              }
            } catch (e) {
              // 继续等待完整的响应
            }
          }
        }
      };

      this.serverProcess.stdout.on('data', onData);
      this.serverProcess.stdin.write(JSON.stringify(request) + '\n');
    });
  }

  async callTool(toolName, args = {}) {
    return this.sendRequest('tools/call', {
      name: toolName,
      arguments: args
    });
  }

  async close() {
    if (this.serverProcess) {
      this.serverProcess.kill();
      this.serverProcess = null;
    }
  }
}

// 测试修正后的路由创建
async function testCorrectRouteType() {
  const client = new SimpleMCPClient();
  
  try {
    await client.startServer();
    
    // 初始化连接
    await client.sendRequest('initialize', {
      protocolVersion: '2024-11-05',
      capabilities: {},
      clientInfo: { name: 'correct-route-type-test', version: '1.0.0' }
    });
    
    console.log('\n🔧 === 测试修正后的路由创建（正确的 tab 类型）===\n');
    
    // 步骤1: 创建使用正确 "tab" 类型的页面
    console.log('📄 步骤1: 创建使用正确 "tab" 类型的商品页面');
    let routeResult = null;
    
    try {
      routeResult = await client.callTool('create_page_route', {
        title: '商品管理（正确类型）',
        icon: 'ShoppingOutlined',
        template: 'blank'
      });
      console.log('✅ 修正后的路由创建成功');
      console.log(routeResult.content[0].text);
    } catch (error) {
      console.log('❌ 创建路由失败:', error.message);
      return;
    }
    
    // 步骤2: 分析路由结构
    console.log('\n🔍 步骤2: 分析路由结构');
    
    const routeText = routeResult.content[0].text;
    let pageUid = null;
    let tabUid = null;
    
    try {
      // 提取页面 UID
      const pageUidMatch = routeText.match(/Page UID: ([a-zA-Z0-9-]+)/);
      if (pageUidMatch) {
        pageUid = pageUidMatch[1];
        console.log(`✅ 页面 UID: ${pageUid}`);
      }
      
      // 提取标签页 UID
      const tabUidMatch = routeText.match(/Tab UID: ([a-zA-Z0-9-]+)/);
      if (tabUidMatch) {
        tabUid = tabUidMatch[1];
        console.log(`✅ 标签页 UID: ${tabUid}`);
      }
      
      // 检查子路由类型
      const hasTabType = routeText.includes('"type": "tab"');
      const hasTabsType = routeText.includes('"type": "tabs"');
      
      console.log(`✅ 子路由类型检查:`);
      console.log(`   - 包含 "tab" 类型: ${hasTabType ? '是' : '否'}`);
      console.log(`   - 包含 "tabs" 类型: ${hasTabsType ? '是' : '否'}`);
      
      if (hasTabType && !hasTabsType) {
        console.log('🎉 类型修正成功！使用了正确的 "tab" 类型');
      } else {
        console.log('⚠️  类型可能仍有问题');
      }
      
    } catch (error) {
      console.log('⚠️  解析路由信息时出错:', error.message);
    }
    
    // 步骤3: 验证路由列表
    console.log('\n📋 步骤3: 验证路由列表');
    try {
      const allRoutes = await client.callTool('list_routes');
      const routesText = allRoutes.content[0].text;
      
      if (routesText.includes('商品管理（正确类型）')) {
        console.log('✅ 新路由已出现在路由列表中');
        
        // 解析路由数据
        const jsonMatch = routesText.match(/\[([\s\S]*)\]/);
        if (jsonMatch) {
          const routesData = JSON.parse(jsonMatch[0]);
          const ourRoute = routesData.find(route => route.title === '商品管理（正确类型）');
          
          if (ourRoute && ourRoute.children && ourRoute.children.length > 0) {
            const childRoute = ourRoute.children[0];
            console.log('✅ 子路由详细信息:');
            console.log(`   类型: ${childRoute.type}`);
            console.log(`   标题: ${childRoute.title}`);
            console.log(`   隐藏: ${childRoute.hidden}`);
            console.log(`   Schema UID: ${childRoute.schemaUid}`);
            console.log(`   Tab Schema Name: ${childRoute.tabSchemaName}`);
            
            if (childRoute.type === 'tab') {
              console.log('🎉 确认：子路由使用了正确的 "tab" 类型！');
            } else {
              console.log(`⚠️  警告：子路由类型为 "${childRoute.type}"，不是预期的 "tab"`);
            }
          }
        }
      } else {
        console.log('⚠️  新路由未在路由列表中找到');
      }
    } catch (error) {
      console.log('❌ 验证路由列表失败:', error.message);
    }
    
    console.log('\n🎉 修正后的路由类型测试完成！');
    console.log('\n📝 测试总结:');
    console.log('✅ 基于 test_routes_api.http 的发现');
    console.log('✅ 修正了子路由类型：从 "tabs" 改为 "tab"');
    console.log('✅ 保持了其他正确的配置');
    console.log('✅ 验证了路由结构的正确性');
    
    console.log('\n💡 关键修正:');
    console.log('• HTTP 文件显示子路由类型应该是 "tab"');
    console.log('• 不是 "tabs"，这是一个重要的区别');
    console.log('• 其他配置（hidden: true, tabSchemaName 等）保持不变');
    
    if (pageUid) {
      console.log(`\n🔗 测试访问链接:`);
      console.log(`https://app.dev.orb.local/apps/mcp_playground/admin/${pageUid}`);
      console.log('\n💡 现在应该能正确显示标签页和添加区块功能了！');
    }
    
  } catch (error) {
    console.error('❌ 测试失败:', error.message);
  } finally {
    await client.close();
    console.log('\n🔚 MCP 服务器已关闭');
  }
}

// 运行测试
if (import.meta.url === `file://${process.argv[1]}`) {
  testCorrectRouteType().catch(console.error);
}
