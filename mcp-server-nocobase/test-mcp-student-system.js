#!/usr/bin/env node

/**
 * 通过 MCP 协议创建学生选课系统的完整演示
 * 使用真正的 MCP 客户端连接到 MCP 服务器
 */

import { spawn } from 'child_process';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// 简单的 MCP 客户端实现
class SimpleMCPClient {
  constructor() {
    this.serverProcess = null;
    this.requestId = 1;
  }

  async startServer() {
    console.log('🚀 启动 MCP 服务器...');
    
    this.serverProcess = spawn('node', [
      path.join(__dirname, 'dist', 'index.js'),
      '--base-url', 'https://app.dev.orb.local/api',
      '--token', 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VySWQiOjEsInJvbGVOYW1lIjoiYWRtaW4iLCJpYXQiOjE3NTQ1MjY4NDAsImV4cCI6MzMzMTIxMjY4NDB9.jub9QcFtDdjFrn3UzrT6PxNA19ZuHWGqIHfcBjdNhQA',
      '--app', 'mcp_playground'
    ], {
      stdio: ['pipe', 'pipe', 'pipe']
    });

    // 等待服务器启动
    await new Promise(resolve => setTimeout(resolve, 2000));
    console.log('✅ MCP 服务器已启动');
  }

  async sendRequest(method, params = {}) {
    return new Promise((resolve, reject) => {
      if (!this.serverProcess) {
        reject(new Error('Server not started'));
        return;
      }

      const request = {
        jsonrpc: '2.0',
        id: this.requestId++,
        method: method,
        params: params
      };

      let responseData = '';
      let errorData = '';

      const timeout = setTimeout(() => {
        reject(new Error('Request timeout'));
      }, 30000);

      const onData = (data) => {
        responseData += data.toString();
        
        // 尝试解析响应
        const lines = responseData.trim().split('\n');
        for (const line of lines) {
          if (line.trim().startsWith('{')) {
            try {
              const response = JSON.parse(line);
              if (response.id === request.id) {
                clearTimeout(timeout);
                this.serverProcess.stdout.off('data', onData);
                this.serverProcess.stderr.off('data', onError);
                
                if (response.error) {
                  reject(new Error(response.error.message || 'MCP Error'));
                } else {
                  resolve(response.result);
                }
                return;
              }
            } catch (e) {
              // 继续等待完整的响应
            }
          }
        }
      };

      const onError = (data) => {
        errorData += data.toString();
      };

      this.serverProcess.stdout.on('data', onData);
      this.serverProcess.stderr.on('data', onError);

      // 发送请求
      this.serverProcess.stdin.write(JSON.stringify(request) + '\n');
    });
  }

  async callTool(toolName, args = {}) {
    return this.sendRequest('tools/call', {
      name: toolName,
      arguments: args
    });
  }

  async close() {
    if (this.serverProcess) {
      this.serverProcess.kill();
      this.serverProcess = null;
    }
  }
}

// 测试步骤定义
const testSteps = [
  {
    name: '初始化 MCP 连接',
    action: async (client) => {
      await client.sendRequest('initialize', {
        protocolVersion: '2024-11-05',
        capabilities: {},
        clientInfo: { name: 'student-system-client', version: '1.0.0' }
      });
    }
  },
  {
    name: '列出可用工具',
    action: async (client) => {
      const result = await client.sendRequest('tools/list');
      console.log('📋 可用工具:', result.tools?.map(t => t.name).join(', '));
    }
  },
  {
    name: '创建学生集合',
    action: async (client) => {
      await client.callTool('create_collection', {
        name: 'mcp_students',
        title: 'MCP演示学生',
        autoGenId: true,
        createdAt: true,
        updatedAt: true
      });
    }
  },
  {
    name: '创建课程集合',
    action: async (client) => {
      await client.callTool('create_collection', {
        name: 'mcp_courses',
        title: 'MCP演示课程',
        autoGenId: true,
        createdAt: true,
        updatedAt: true
      });
    }
  },
  {
    name: '创建教师集合',
    action: async (client) => {
      await client.callTool('create_collection', {
        name: 'mcp_teachers',
        title: 'MCP演示教师',
        autoGenId: true,
        createdAt: true,
        updatedAt: true
      });
    }
  },
  {
    name: '为学生添加姓名字段',
    action: async (client) => {
      await client.callTool('create_field', {
        collection: 'mcp_students',
        name: 'name',
        type: 'string',
        interface: 'input',
        uiSchema: {
          title: '学生姓名',
          'x-component': 'Input',
          required: true
        }
      });
    }
  },
  {
    name: '为学生添加学号字段',
    action: async (client) => {
      await client.callTool('create_field', {
        collection: 'mcp_students',
        name: 'student_id',
        type: 'string',
        interface: 'input',
        uiSchema: {
          title: '学号',
          'x-component': 'Input'
        }
      });
    }
  },
  {
    name: '为课程添加名称字段',
    action: async (client) => {
      await client.callTool('create_field', {
        collection: 'mcp_courses',
        name: 'name',
        type: 'string',
        interface: 'input',
        uiSchema: {
          title: '课程名称',
          'x-component': 'Input',
          required: true
        }
      });
    }
  },
  {
    name: '为课程添加学分字段',
    action: async (client) => {
      await client.callTool('create_field', {
        collection: 'mcp_courses',
        name: 'credits',
        type: 'integer',
        interface: 'number',
        uiSchema: {
          title: '学分',
          'x-component': 'InputNumber'
        }
      });
    }
  },
  {
    name: '为教师添加姓名字段',
    action: async (client) => {
      await client.callTool('create_field', {
        collection: 'mcp_teachers',
        name: 'name',
        type: 'string',
        interface: 'input',
        uiSchema: {
          title: '教师姓名',
          'x-component': 'Input',
          required: true
        }
      });
    }
  },
  {
    name: '创建课程-教师关联字段(belongsTo)',
    action: async (client) => {
      await client.callTool('create_field', {
        collection: 'mcp_courses',
        name: 'teacher',
        type: 'belongsTo',
        interface: 'm2o',
        target: 'mcp_teachers',
        foreignKey: 'teacherId',
        targetKey: 'id',
        uiSchema: {
          title: '授课教师',
          'x-component': 'AssociationField'
        }
      });
    }
  },
  {
    name: '创建学生-课程关联字段(belongsTo)',
    action: async (client) => {
      await client.callTool('create_field', {
        collection: 'mcp_students',
        name: 'course',
        type: 'belongsTo',
        interface: 'm2o',
        target: 'mcp_courses',
        foreignKey: 'courseId',
        targetKey: 'id',
        uiSchema: {
          title: '选修课程',
          'x-component': 'AssociationField'
        }
      });
    }
  },
  {
    name: '创建教师记录-张教授',
    action: async (client) => {
      const result = await client.callTool('create_record', {
        collection: 'mcp_teachers',
        data: {
          name: '张教授'
        }
      });
      console.log('✅ 创建教师记录:', result);
      return result;
    }
  },
  {
    name: '创建教师记录-李老师',
    action: async (client) => {
      const result = await client.callTool('create_record', {
        collection: 'mcp_teachers',
        data: {
          name: '李老师'
        }
      });
      console.log('✅ 创建教师记录:', result);
      return result;
    }
  },
  {
    name: '创建课程记录-高等数学',
    action: async (client) => {
      const result = await client.callTool('create_record', {
        collection: 'mcp_courses',
        data: {
          name: '高等数学',
          credits: 4,
          teacherId: 1  // 假设张教授的ID是1
        }
      });
      console.log('✅ 创建课程记录:', result);
      return result;
    }
  },
  {
    name: '创建课程记录-线性代数',
    action: async (client) => {
      const result = await client.callTool('create_record', {
        collection: 'mcp_courses',
        data: {
          name: '线性代数',
          credits: 3,
          teacherId: 2  // 假设李老师的ID是2
        }
      });
      console.log('✅ 创建课程记录:', result);
      return result;
    }
  },
  {
    name: '创建学生记录-王小明',
    action: async (client) => {
      const result = await client.callTool('create_record', {
        collection: 'mcp_students',
        data: {
          name: '王小明',
          student_id: 'S001',
          courseId: 1  // 选修高等数学
        }
      });
      console.log('✅ 创建学生记录:', result);
      return result;
    }
  },
  {
    name: '创建学生记录-李小红',
    action: async (client) => {
      const result = await client.callTool('create_record', {
        collection: 'mcp_students',
        data: {
          name: '李小红',
          student_id: 'S002',
          courseId: 1  // 选修高等数学
        }
      });
      console.log('✅ 创建学生记录:', result);
      return result;
    }
  },
  {
    name: '查询学生记录（包含关联数据）',
    action: async (client) => {
      const result = await client.callTool('list_records', {
        collection: 'mcp_students',
        appends: ['course', 'course.teacher']
      });
      console.log('📊 学生记录（含关联）:', JSON.stringify(result, null, 2));
      return result;
    }
  }
];

// 主测试函数
async function runMCPTest() {
  const client = new SimpleMCPClient();
  
  try {
    await client.startServer();
    
    console.log('\n🎓 === 通过 MCP 协议创建学生选课系统 ===\n');
    
    for (let i = 0; i < testSteps.length; i++) {
      const step = testSteps[i];
      console.log(`\n📋 步骤 ${i + 1}: ${step.name}`);
      
      try {
        const result = await step.action(client);
        console.log(`✅ ${step.name} - 成功`);
        
        // 如果有返回结果且不是查询操作，简单显示
        if (result && !step.name.includes('查询') && !step.name.includes('列出')) {
          console.log(`   结果: ${JSON.stringify(result).substring(0, 100)}...`);
        }
      } catch (error) {
        console.log(`❌ ${step.name} - 失败: ${error.message}`);
        // 某些步骤失败不影响后续测试
        if (step.name.includes('创建') && error.message.includes('exists')) {
          console.log('   (资源已存在，继续执行)');
        }
      }
    }
    
    console.log('\n🎉 学生选课系统创建完成！');
    console.log('\n📊 系统包含：');
    console.log('• 3个集合：mcp_students, mcp_courses, mcp_teachers');
    console.log('• 多个字段：姓名、学号、课程名称、学分等');
    console.log('• 关联关系：学生-课程(belongsTo), 课程-教师(belongsTo)');
    console.log('• 测试数据：2名教师、2门课程、2名学生');
    
  } catch (error) {
    console.error('❌ 测试失败:', error.message);
  } finally {
    await client.close();
    console.log('\n🔚 MCP 服务器已关闭');
  }
}

// 运行测试
if (import.meta.url === `file://${process.argv[1]}`) {
  runMCPTest().catch(console.error);
}
