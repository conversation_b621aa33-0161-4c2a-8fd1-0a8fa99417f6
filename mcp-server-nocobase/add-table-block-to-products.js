#!/usr/bin/env node

/**
 * 为商品页面添加表格区块 - 展示商品集合数据
 */

import { spawn } from 'child_process';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// 简单的 MCP 客户端实现
class SimpleMCPClient {
  constructor() {
    this.serverProcess = null;
    this.requestId = 1;
  }

  async startServer() {
    console.log('🚀 启动 MCP 服务器...');
    
    this.serverProcess = spawn('node', [
      path.join(__dirname, 'dist', 'index.js'),
      '--base-url', 'https://app.dev.orb.local/api',
      '--token', 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VySWQiOjEsInJvbGVOYW1lIjoiYWRtaW4iLCJpYXQiOjE3NTQ1MjY4NDAsImV4cCI6MzMzMTIxMjY4NDB9.jub9QcFtDdjFrn3UzrT6PxNA19ZuHWGqIHfcBjdNhQA',
      '--app', 'mcp_playground'
    ], {
      stdio: ['pipe', 'pipe', 'pipe']
    });

    // 等待服务器启动
    await new Promise(resolve => setTimeout(resolve, 2000));
    console.log('✅ MCP 服务器已启动');
  }

  async sendRequest(method, params = {}) {
    return new Promise((resolve, reject) => {
      if (!this.serverProcess) {
        reject(new Error('Server not started'));
        return;
      }

      const request = {
        jsonrpc: '2.0',
        id: this.requestId++,
        method: method,
        params: params
      };

      let responseData = '';
      const timeout = setTimeout(() => {
        reject(new Error('Request timeout'));
      }, 30000);

      const onData = (data) => {
        responseData += data.toString();
        
        const lines = responseData.trim().split('\n');
        for (const line of lines) {
          if (line.trim().startsWith('{')) {
            try {
              const response = JSON.parse(line);
              if (response.id === request.id) {
                clearTimeout(timeout);
                this.serverProcess.stdout.off('data', onData);
                
                if (response.error) {
                  reject(new Error(response.error.message || 'MCP Error'));
                } else {
                  resolve(response.result);
                }
                return;
              }
            } catch (e) {
              // 继续等待完整的响应
            }
          }
        }
      };

      this.serverProcess.stdout.on('data', onData);
      this.serverProcess.stdin.write(JSON.stringify(request) + '\n');
    });
  }

  async callTool(toolName, args = {}) {
    return this.sendRequest('tools/call', {
      name: toolName,
      arguments: args
    });
  }

  async close() {
    if (this.serverProcess) {
      this.serverProcess.kill();
      this.serverProcess = null;
    }
  }
}

// 添加表格区块到商品页面
async function addTableBlockToProductsPage() {
  const client = new SimpleMCPClient();
  
  try {
    await client.startServer();
    
    // 初始化连接
    await client.sendRequest('initialize', {
      protocolVersion: '2024-11-05',
      capabilities: {},
      clientInfo: { name: 'products-table-block-client', version: '1.0.0' }
    });
    
    console.log('\n📊 === 为商品页面添加表格区块 ===\n');
    
    // 步骤1: 查看现有的路由
    console.log('🔍 步骤1: 查看现有路由');
    try {
      const routesResult = await client.callTool('list_routes', { tree: true });
      console.log('✅ 路由列表获取成功');
      
      // 查找商品管理路由
      const routes = JSON.parse(routesResult.content[0].text);
      const productsRoute = findRouteByTitle(routes, '商品管理');
      
      if (productsRoute) {
        console.log(`✅ 找到商品管理路由: ID ${productsRoute.id}, 标题: ${productsRoute.title}`);
        console.log(`   页面UID: ${productsRoute.schemaUid}`);
        console.log(`   Tabs支持: ${productsRoute.enableTabs ? '已启用' : '未启用'}`);
      } else {
        console.log('❌ 未找到商品管理路由，需要先创建路由');
        return;
      }
    } catch (error) {
      console.log('❌ 查看路由失败:', error.message);
      return;
    }
    
    // 步骤2: 查看商品集合数据
    console.log('\n📦 步骤2: 查看商品集合数据');
    try {
      const collectionsResult = await client.callTool('list_collections', { includeMeta: true });
      console.log('✅ 集合列表获取成功');
      
      // 查找products集合
      const collectionsText = collectionsResult.content[0].text;
      const productsCollection = findProductsCollection(collectionsText);
      
      if (productsCollection) {
        console.log(`✅ 找到商品集合: ${productsCollection.name}`);
        console.log(`   字段数量: ${productsCollection.fields?.length || '未知'}`);
      } else {
        console.log('❌ 未找到products集合');
        return;
      }
    } catch (error) {
      console.log('❌ 查看集合失败:', error.message);
      return;
    }
    
    // 步骤3: 查看商品数据
    console.log('\n🛍️ 步骤3: 查看商品数据');
    try {
      const recordsResult = await client.callTool('list_records', {
        collection: 'products',
        filter: {},
        sort: ['-createdAt']
      });
      console.log('✅ 商品数据获取成功');
      console.log(recordsResult.content[0].text);
    } catch (error) {
      console.log('❌ 查看商品数据失败:', error.message);
    }
    
    // 步骤4: 创建表格区块
    console.log('\n🔧 步骤4: 创建表格区块');
    try {
      // 首先查看现有的区块
      const blocksResult = await client.callTool('list_blocks', {
        pageUid: 'page-1754577831266-6ssv7j36v' // 商品页面的UID
      });
      console.log('✅ 现有区块列表获取成功');
      console.log(blocksResult.content[0].text);
      
      // 创建表格区块
      const tableBlockResult = await client.callTool('create_block', {
        pageUid: 'page-1754577831266-6ssv7j36v',
        blockType: 'table',
        collection: 'products',
        title: '商品列表',
        settings: {
          showIndex: true,
          rowSelection: true,
          search: true,
          filter: true,
          actions: ['create', 'edit', 'delete', 'view']
        }
      });
      console.log('✅ 表格区块创建成功');
      console.log(tableBlockResult.content[0].text);
    } catch (error) {
      console.log('❌ 创建表格区块失败:', error.message);
      
      // 如果create_block工具不存在，尝试使用其他方法
      console.log('\n🔄 尝试使用schema更新方法...');
      try {
        const schemaUpdateResult = await client.callTool('update_page_schema', {
          pageUid: 'page-1754577831266-6ssv7j36v',
          schema: {
            type: 'void',
            'x-component': 'Page',
            properties: {
              grid: {
                type: 'void',
                'x-component': 'Grid',
                properties: {
                  table: {
                    type: 'void',
                    'x-component': 'TableBlockProvider',
                    'x-component-props': {
                      collection: 'products',
                      action: 'list',
                      params: {
                        sort: ['-createdAt'],
                        filter: {}
                      }
                    },
                    properties: {
                      table: {
                        type: 'array',
                        'x-component': 'TableV2',
                        'x-component-props': {
                          rowKey: 'id',
                          rowSelection: {
                            type: 'checkbox'
                          },
                          useProps: () => {
                            return {
                              columns: [
                                {
                                  title: '商品名称',
                                  dataIndex: 'name',
                                  key: 'name'
                                },
                                {
                                  title: '价格',
                                  dataIndex: 'price',
                                  key: 'price',
                                  render: (text) => `¥${text}`
                                },
                                {
                                  title: '分类',
                                  dataIndex: 'category',
                                  key: 'category'
                                },
                                {
                                  title: '库存',
                                  dataIndex: 'stock',
                                  key: 'stock'
                                },
                                {
                                  title: '状态',
                                  dataIndex: 'status',
                                  key: 'status'
                                },
                                {
                                  title: '创建时间',
                                  dataIndex: 'createdAt',
                                  key: 'createdAt',
                                  render: (text) => new Date(text).toLocaleString()
                                }
                              ]
                            };
                          }
                        }
                      }
                    }
                  }
                }
              }
            }
          }
        });
        console.log('✅ Schema更新成功');
        console.log(schemaUpdateResult.content[0].text);
      } catch (schemaError) {
        console.log('❌ Schema更新也失败:', schemaError.message);
      }
    }
    
    console.log('\n🎉 表格区块添加完成！');
    console.log('\n📝 总结:');
    console.log('✅ 成功查看了商品管理路由');
    console.log('✅ 成功查看了products集合');
    console.log('✅ 成功查看了商品数据');
    console.log('✅ 尝试创建表格区块');
    console.log('\n💡 提示:');
    console.log('• 请在NocoBase管理界面中查看商品页面');
    console.log('• 如果表格区块未显示，可能需要手动添加');
    console.log('• 页面应该已经包含products集合的数据表格');
    
  } catch (error) {
    console.error('❌ 添加表格区块失败:', error.message);
  } finally {
    await client.close();
    console.log('\n🔚 MCP 服务器已关闭');
  }
}

// 辅助函数：查找路由
function findRouteByTitle(routes, title) {
  for (const route of routes) {
    if (route.title === title) {
      return route;
    }
    if (route.children) {
      const found = findRouteByTitle(route.children, title);
      if (found) return found;
    }
  }
  return null;
}

// 辅助函数：查找products集合
function findProductsCollection(collectionsText) {
  const lines = collectionsText.split('\n');
  let currentCollection = null;
  
  for (const line of lines) {
    if (line.startsWith('• ')) {
      const match = line.match(/• ([^(]+) \(([^)]+)\)/);
      if (match) {
        const name = match[1].trim();
        if (name.includes('products')) {
          currentCollection = { name, title: match[2] };
        }
      }
    } else if (line.startsWith('  Fields: ') && currentCollection) {
      const fieldsMatch = line.match(/Fields: (\d+)/);
      if (fieldsMatch) {
        currentCollection.fields = parseInt(fieldsMatch[1]);
        return currentCollection;
      }
    }
  }
  
  return null;
}

// 运行脚本
if (import.meta.url === `file://${process.argv[1]}`) {
  addTableBlockToProductsPage().catch(console.error);
}