#!/usr/bin/env node

/**
 * 使用 Playwright 手动添加菜单项并观察 API 请求
 */

import { chromium } from 'playwright';

async function debugMenuCreation() {
  console.log('🚀 启动 Playwright 调试...');
  
  const browser = await chromium.launch({ 
    headless: false,  // 显示浏览器窗口
    slowMo: 1000      // 减慢操作速度便于观察
  });
  
  const context = await browser.newContext();
  const page = await context.newPage();
  
  // 监听所有网络请求
  const requests = [];
  page.on('request', request => {
    if (request.url().includes('/api/')) {
      requests.push({
        method: request.method(),
        url: request.url(),
        headers: request.headers(),
        postData: request.postData()
      });
      console.log(`📡 API 请求: ${request.method()} ${request.url()}`);
      if (request.postData()) {
        console.log(`📝 请求数据: ${request.postData()}`);
      }
    }
  });
  
  page.on('response', response => {
    if (response.url().includes('/api/')) {
      console.log(`📨 API 响应: ${response.status()} ${response.url()}`);
    }
  });
  
  try {
    console.log('🌐 访问 NocoBase 管理界面...');
    await page.goto('https://app.dev.orb.local/apps/mcp_playground/admin');
    
    console.log('⏳ 等待页面加载...');
    await page.waitForTimeout(3000);
    
    // 检查是否需要登录
    const loginButton = await page.$('button[type="submit"]');
    if (loginButton) {
      console.log('🔐 需要登录，输入凭据...');
      await page.fill('input[name="email"]', 'neo');
      await page.fill('input[name="password"]', 'neo@123');
      await page.click('button[type="submit"]');
      await page.waitForTimeout(3000);
    }
    
    console.log('🎯 寻找菜单配置入口...');
    
    // 等待页面完全加载
    await page.waitForLoadState('networkidle');
    
    // 寻找设置或配置按钮
    const settingsSelectors = [
      '[data-testid="settings"]',
      'button[aria-label*="设置"]',
      'button[aria-label*="Settings"]',
      '.ant-menu-item:has-text("设置")',
      '.ant-menu-item:has-text("Settings")',
      '[title="设置"]',
      '[title="Settings"]'
    ];
    
    let settingsButton = null;
    for (const selector of settingsSelectors) {
      try {
        settingsButton = await page.$(selector);
        if (settingsButton) {
          console.log(`✅ 找到设置按钮: ${selector}`);
          break;
        }
      } catch (e) {
        // 继续尝试下一个选择器
      }
    }
    
    if (!settingsButton) {
      console.log('🔍 未找到设置按钮，尝试查找所有可点击元素...');
      
      // 截图以便调试
      await page.screenshot({ path: 'debug-screenshot.png', fullPage: true });
      console.log('📸 已保存截图: debug-screenshot.png');
      
      // 列出所有可能的菜单项
      const menuItems = await page.$$eval('.ant-menu-item, .ant-btn, button', elements => 
        elements.map(el => ({
          text: el.textContent?.trim(),
          className: el.className,
          title: el.title,
          ariaLabel: el.getAttribute('aria-label')
        })).filter(item => item.text)
      );
      
      console.log('📋 找到的菜单项:');
      menuItems.forEach((item, index) => {
        console.log(`  ${index + 1}. "${item.text}" (${item.className})`);
      });
    }
    
    // 如果找到设置按钮，点击它
    if (settingsButton) {
      console.log('🖱️ 点击设置按钮...');
      await settingsButton.click();
      await page.waitForTimeout(2000);
      
      // 寻找菜单配置选项
      const menuConfigSelectors = [
        'text="菜单配置"',
        'text="Menu Configuration"',
        'text="路由"',
        'text="Routes"',
        '.ant-menu-item:has-text("菜单")',
        '.ant-menu-item:has-text("Menu")'
      ];
      
      for (const selector of menuConfigSelectors) {
        try {
          const menuConfig = await page.$(selector);
          if (menuConfig) {
            console.log(`✅ 找到菜单配置: ${selector}`);
            await menuConfig.click();
            await page.waitForTimeout(2000);
            break;
          }
        } catch (e) {
          // 继续尝试
        }
      }
    }
    
    // 寻找添加菜单的按钮
    console.log('➕ 寻找添加菜单按钮...');
    const addButtonSelectors = [
      'button:has-text("添加")',
      'button:has-text("Add")',
      'button:has-text("新建")',
      'button:has-text("Create")',
      '.ant-btn-primary',
      '[data-testid="add-menu"]'
    ];
    
    let addButton = null;
    for (const selector of addButtonSelectors) {
      try {
        addButton = await page.$(selector);
        if (addButton) {
          console.log(`✅ 找到添加按钮: ${selector}`);
          break;
        }
      } catch (e) {
        // 继续尝试
      }
    }
    
    if (addButton) {
      console.log('🖱️ 点击添加按钮...');
      await addButton.click();
      await page.waitForTimeout(2000);
      
      // 填写菜单信息
      console.log('📝 填写菜单信息...');
      
      // 尝试填写标题
      const titleSelectors = [
        'input[name="title"]',
        'input[placeholder*="标题"]',
        'input[placeholder*="Title"]',
        '.ant-input'
      ];
      
      for (const selector of titleSelectors) {
        try {
          const titleInput = await page.$(selector);
          if (titleInput) {
            await titleInput.fill('Playwright测试菜单');
            console.log('✅ 已填写标题');
            break;
          }
        } catch (e) {
          // 继续尝试
        }
      }
      
      // 寻找并点击保存按钮
      const saveSelectors = [
        'button:has-text("保存")',
        'button:has-text("Save")',
        'button:has-text("确定")',
        'button:has-text("OK")',
        '.ant-btn-primary'
      ];
      
      for (const selector of saveSelectors) {
        try {
          const saveButton = await page.$(selector);
          if (saveButton) {
            console.log('💾 点击保存按钮...');
            await saveButton.click();
            await page.waitForTimeout(3000);
            break;
          }
        } catch (e) {
          // 继续尝试
        }
      }
    }
    
    console.log('\n📊 捕获的 API 请求总结:');
    requests.forEach((req, index) => {
      console.log(`\n${index + 1}. ${req.method} ${req.url}`);
      if (req.postData) {
        try {
          const data = JSON.parse(req.postData);
          console.log('   数据:', JSON.stringify(data, null, 2));
        } catch (e) {
          console.log('   数据:', req.postData);
        }
      }
    });
    
    // 保持浏览器打开一段时间以便手动操作
    console.log('\n⏰ 浏览器将保持打开60秒，您可以手动操作并观察请求...');
    await page.waitForTimeout(60000);
    
  } catch (error) {
    console.error('❌ 调试过程中出错:', error.message);
    
    // 截图保存错误状态
    try {
      await page.screenshot({ path: 'error-screenshot.png', fullPage: true });
      console.log('📸 已保存错误截图: error-screenshot.png');
    } catch (e) {
      // 忽略截图错误
    }
  } finally {
    await browser.close();
    console.log('🔚 浏览器已关闭');
  }
}

// 运行调试
if (import.meta.url === `file://${process.argv[1]}`) {
  debugMenuCreation().catch(console.error);
}
