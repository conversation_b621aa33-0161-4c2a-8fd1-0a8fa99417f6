#!/usr/bin/env node

/**
 * 创建商品管理页面 - 包含路由、页面和商品区块
 */

import { spawn } from 'child_process';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// 简单的 MCP 客户端实现
class SimpleMCPClient {
  constructor() {
    this.serverProcess = null;
    this.requestId = 1;
  }

  async startServer() {
    console.log('🚀 启动 MCP 服务器...');
    
    this.serverProcess = spawn('node', [
      path.join(__dirname, 'dist', 'index.js'),
      '--base-url', 'https://app.dev.orb.local/api',
      '--token', 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VySWQiOjEsInJvbGVOYW1lIjoiYWRtaW4iLCJpYXQiOjE3NTQ1MjY4NDAsImV4cCI6MzMzMTIxMjY4NDB9.jub9QcFtDdjFrn3UzrT6PxNA19ZuHWGqIHfcBjdNhQA',
      '--app', 'mcp_playground'
    ], {
      stdio: ['pipe', 'pipe', 'pipe']
    });

    // 等待服务器启动
    await new Promise(resolve => setTimeout(resolve, 2000));
    console.log('✅ MCP 服务器已启动');
  }

  async sendRequest(method, params = {}) {
    return new Promise((resolve, reject) => {
      if (!this.serverProcess) {
        reject(new Error('Server not started'));
        return;
      }

      const request = {
        jsonrpc: '2.0',
        id: this.requestId++,
        method: method,
        params: params
      };

      let responseData = '';
      const timeout = setTimeout(() => {
        reject(new Error('Request timeout'));
      }, 30000);

      const onData = (data) => {
        responseData += data.toString();
        
        const lines = responseData.trim().split('\n');
        for (const line of lines) {
          if (line.trim().startsWith('{')) {
            try {
              const response = JSON.parse(line);
              if (response.id === request.id) {
                clearTimeout(timeout);
                this.serverProcess.stdout.off('data', onData);
                
                if (response.error) {
                  reject(new Error(response.error.message || 'MCP Error'));
                } else {
                  resolve(response.result);
                }
                return;
              }
            } catch (e) {
              // 继续等待完整的响应
            }
          }
        }
      };

      this.serverProcess.stdout.on('data', onData);
      this.serverProcess.stdin.write(JSON.stringify(request) + '\n');
    });
  }

  async callTool(toolName, args = {}) {
    return this.sendRequest('tools/call', {
      name: toolName,
      arguments: args
    });
  }

  async close() {
    if (this.serverProcess) {
      this.serverProcess.kill();
      this.serverProcess = null;
    }
  }
}

// 创建商品管理页面
async function createProductsPage() {
  const client = new SimpleMCPClient();
  
  try {
    await client.startServer();
    
    // 初始化连接
    await client.sendRequest('initialize', {
      protocolVersion: '2024-11-05',
      capabilities: {},
      clientInfo: { name: 'products-page-client', version: '1.0.0' }
    });
    
    console.log('\n🏪 === 创建商品管理页面 ===\n');
    
    // 步骤1: 查找商品集合
    console.log('🔍 步骤1: 查找商品集合');
    let productCollection = null;
    
    try {
      const collections = await client.callTool('list_collections');
      const collectionsText = collections.content[0].text;
      
      // 查找最新的商品集合
      const productMatches = collectionsText.match(/• (products_\d+) \(/g);
      if (productMatches && productMatches.length > 0) {
        productCollection = productMatches[productMatches.length - 1].match(/products_\d+/)[0];
        console.log(`✅ 找到商品集合: ${productCollection}`);
      } else {
        console.log('❌ 未找到商品集合，请先运行 test-products-collection.js');
        return;
      }
    } catch (error) {
      console.log('❌ 查找集合失败:', error.message);
      return;
    }
    
    // 步骤2: 创建商品管理路由
    console.log('\n🛣️  步骤2: 创建商品管理路由');
    let routeResult = null;
    
    try {
      routeResult = await client.callTool('create_page_route', {
        title: '商品管理',
        icon: 'ShoppingOutlined',
        template: 'blank'
      });
      console.log('✅ 路由创建成功');
      console.log('   ', routeResult.content[0].text);
    } catch (error) {
      console.log('❌ 创建路由失败:', error.message);
      return;
    }
    
    // 从返回结果中提取页面 UID
    let pageUid = null;
    try {
      const routeText = routeResult.content[0].text;
      // 尝试从 JSON 响应中提取 schemaUid
      const schemaUidMatch = routeText.match(/"schemaUid":\s*"([^"]+)"/);
      if (schemaUidMatch) {
        pageUid = schemaUidMatch[1];
        console.log(`   页面 UID: ${pageUid}`);
      } else {
        // 尝试其他可能的 UID 格式
        const uidMatch = routeText.match(/page-[0-9]+-[a-zA-Z0-9]+/);
        if (uidMatch) {
          pageUid = uidMatch[0];
          console.log(`   页面 UID: ${pageUid}`);
        }
      }
    } catch (error) {
      console.log('⚠️  无法提取页面 UID，将尝试获取页面信息');
    }
    
    // 步骤3: 获取页面结构（如果没有 UID）
    if (!pageUid) {
      console.log('\n📄 步骤3: 获取页面结构');
      try {
        const routes = await client.callTool('list_routes');
        console.log('✅ 路由列表获取成功，请查看最新创建的商品管理路由');
        // 这里可以进一步解析路由信息获取 pageUid
      } catch (error) {
        console.log('❌ 获取路由列表失败:', error.message);
      }
    }
    
    // 步骤4: 在页面上添加商品表格区块
    console.log('\n📊 步骤4: 添加商品表格区块');
    
    if (pageUid) {
      try {
        const tableBlockResult = await client.callTool('add_table_block', {
          parentUid: pageUid,
          collectionName: productCollection,
          title: '商品列表',
          showActions: true,
          showPagination: true,
          pageSize: 20
        });
        console.log('✅ 商品表格区块添加成功');
        console.log('   ', tableBlockResult.content[0].text);
      } catch (error) {
        console.log('❌ 添加表格区块失败:', error.message);
        console.log('   可能的原因: 页面 UID 无效或区块添加功能需要调整');
      }
    } else {
      console.log('⚠️  跳过区块添加，因为无法获取页面 UID');
    }
    
    // 步骤5: 添加商品表单区块（用于新增商品）
    console.log('\n📝 步骤5: 添加商品表单区块');
    
    if (pageUid) {
      try {
        const formBlockResult = await client.callTool('add_form_block', {
          parentUid: pageUid,
          collectionName: productCollection,
          title: '添加商品',
          type: 'create'
        });
        console.log('✅ 商品表单区块添加成功');
        console.log('   ', formBlockResult.content[0].text);
      } catch (error) {
        console.log('❌ 添加表单区块失败:', error.message);
      }
    } else {
      console.log('⚠️  跳过表单区块添加，因为无法获取页面 UID');
    }
    
    // 步骤6: 添加 Markdown 说明区块
    console.log('\n📋 步骤6: 添加页面说明区块');
    
    if (pageUid) {
      try {
        const markdownContent = `# 🛍️ 商品管理系统

欢迎使用商品管理系统！这个页面通过 MCP 协议自动创建。

## 功能特性

✅ **完整的商品信息管理**
- 商品名称、价格、描述
- 商品分类和库存管理
- 商品状态控制

✅ **自动化的数据管理**
- 自动生成 ID
- 自动记录创建时间和更新时间
- 自动关联创建人和更新人

✅ **现代化的界面**
- 响应式表格显示
- 便捷的表单操作
- 实时数据更新

## 使用说明

1. 使用下方表格查看所有商品
2. 使用表单添加新商品
3. 点击表格中的操作按钮编辑或删除商品

---
*此页面由 NocoBase MCP Server 自动生成*`;

        const markdownBlockResult = await client.callTool('add_markdown_block', {
          parentUid: pageUid,
          title: '系统说明',
          content: markdownContent
        });
        console.log('✅ 说明区块添加成功');
        console.log('   ', markdownBlockResult.content[0].text);
      } catch (error) {
        console.log('❌ 添加说明区块失败:', error.message);
      }
    } else {
      console.log('⚠️  跳过说明区块添加，因为无法获取页面 UID');
    }
    
    // 步骤7: 查看最终的页面结构
    console.log('\n🏗️  步骤7: 查看页面结构');
    
    if (pageUid) {
      try {
        const pageBlocks = await client.callTool('list_page_blocks', {
          schemaUid: pageUid
        });
        console.log('✅ 页面区块列表:');
        console.log('   ', pageBlocks.content[0].text);
      } catch (error) {
        console.log('❌ 获取页面区块失败:', error.message);
      }
    }
    
    console.log('\n🎉 商品管理页面创建完成！');
    console.log('\n📝 创建总结:');
    console.log(`✅ 使用商品集合: ${productCollection}`);
    console.log('✅ 创建了商品管理路由');
    console.log('✅ 创建了空白页面模板');
    if (pageUid) {
      console.log(`✅ 页面 UID: ${pageUid}`);
      console.log('✅ 添加了商品表格区块');
      console.log('✅ 添加了商品表单区块');
      console.log('✅ 添加了页面说明区块');
    }
    
    console.log('\n🔗 访问方式:');
    console.log('💡 请在 NocoBase 管理界面中查看新创建的"商品管理"页面');
    console.log('💡 页面包含完整的商品 CRUD 功能');
    console.log('💡 所有默认字段都会在界面中正确显示');
    
  } catch (error) {
    console.error('❌ 创建页面失败:', error.message);
  } finally {
    await client.close();
    console.log('\n🔚 MCP 服务器已关闭');
  }
}

// 运行测试
if (import.meta.url === `file://${process.argv[1]}`) {
  createProductsPage().catch(console.error);
}
