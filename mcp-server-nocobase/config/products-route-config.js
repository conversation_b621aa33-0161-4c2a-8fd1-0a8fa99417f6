/**
 * 商品页面路由配置
 * 定义创建商品页面路由的标准配置
 */

export const PRODUCTS_ROUTE_CONFIG = {
  // 基本页面配置
  page: {
    title: '商品管理',
    icon: 'ShoppingCartOutlined',
    template: 'table',
    collectionName: 'products',
    enableTabs: true,
    hidden: false
  },
  
  // 页面描述
  description: {
    name: '商品管理页面',
    purpose: '用于展示和管理商品信息',
    features: [
      '商品数据表格展示',
      '完整的CRUD操作',
      '多标签页支持',
      '响应式布局'
    ]
  },
  
  // 预期的集合字段
  expectedFields: [
    'id',
    'name',
    'price',
    'description',
    'category',
    'stock',
    'status',
    'createdAt',
    'updatedAt',
    'createdBy',
    'updatedBy'
  ],
  
  // 表格配置
  table: {
    showActions: true,
    showPagination: true,
    pageSize: 20,
    rowSelection: {
      type: 'checkbox'
    },
    columns: [
      { key: 'name', title: '商品名称', width: 200 },
      { key: 'price', title: '价格', width: 120 },
      { key: 'category', title: '分类', width: 120 },
      { key: 'stock', title: '库存', width: 100 },
      { key: 'status', title: '状态', width: 100 },
      { key: 'createdAt', title: '创建时间', width: 150 }
    ]
  },
  
  // Tabs配置
  tabs: {
    defaultTab: {
      title: '商品列表',
      key: 'products-list'
    },
    additionalTabs: [
      {
        title: '商品统计',
        key: 'products-stats'
      },
      {
        title: '商品分类',
        key: 'products-categories'
      }
    ]
  },
  
  // 权限配置
  permissions: {
    allowCreate: true,
    allowRead: true,
    allowUpdate: true,
    allowDelete: true
  },
  
  // 备选配置（如果主配置失败）
  fallbackConfigs: [
    {
      title: '商品管理',
      icon: 'ShoppingCartOutlined',
      template: 'blank',
      enableTabs: true,
      hidden: false,
      reason: '如果table模板不可用，使用blank模板'
    },
    {
      title: 'Products',
      icon: 'ShoppingOutlined',
      template: 'table',
      collectionName: 'products',
      enableTabs: false,
      hidden: false,
      reason: '如果中文标题有问题，使用英文标题'
    }
  ]
};

// 导出工具函数
export const getRouteConfig = (overrides = {}) => {
  return {
    ...PRODUCTS_ROUTE_CONFIG.page,
    ...overrides
  };
};

export const getFallbackConfigs = () => {
  return PRODUCTS_ROUTE_CONFIG.fallbackConfigs;
};

export const getExpectedFields = () => {
  return PRODUCTS_ROUTE_CONFIG.expectedFields;
};

export const getTableConfig = () => {
  return PRODUCTS_ROUTE_CONFIG.table;
};

export const getTabsConfig = () => {
  return PRODUCTS_ROUTE_CONFIG.tabs;
};

export default PRODUCTS_ROUTE_CONFIG;