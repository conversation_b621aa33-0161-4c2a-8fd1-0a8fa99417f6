# 商品页面路由创建说明

## 概述

这个脚本使用NocoBase MCP工具创建一个完整的商品页面路由，具有以下特性：

- **页面标题**: "商品管理"
- **页面图标**: ShoppingCartOutlined
- **页面模板**: table (用于展示商品集合数据)
- **数据集合**: products (或现有的商品集合)
- **Tabs功能**: 启用
- **页面类型**: 页面路由

## 创建的页面功能

1. **自动数据表格**: 使用table模板自动创建数据表格
2. **商品集合展示**: 展示products集合的所有数据
3. **Tabs支持**: 支持多标签页功能
4. **完整的CRUD操作**: 自动支持创建、读取、更新、删除操作

## 使用方法

### 方法1: 使用完整脚本
```bash
cd /Users/<USER>/dev/mcp-nocobase/mcp-server-nocobase
npm run build
node create-products-route.js
```

### 方法2: 使用简化脚本
```bash
cd /Users/<USER>/dev/mcp-nocobase/mcp-server-nocobase
npm run build
node create-route-simple.js
```

## 前置要求

1. **NocoBase服务器**: 需要运行NocoBase实例
2. **MCP服务器**: 需要构建并启动MCP服务器
3. **访问权限**: 需要有效的API token
4. **products集合**: 需要存在products集合（脚本会自动检查）

## 脚本执行流程

1. **启动MCP服务器**: 自动启动NocoBase MCP服务器
2. **查看现有集合**: 列出所有可用集合，查找products集合
3. **创建页面路由**: 使用create_page_route工具创建路由
4. **配置页面属性**: 设置标题、图标、模板等属性
5. **启用Tabs功能**: 自动启用多标签页支持
6. **返回结果**: 显示创建结果和页面信息

## 创建的页面结构

```
商品管理 (ShoppingCartOutlined)
├── 主页面 (table模板)
│   ├── 商品数据表格
│   ├── 分页控件
│   ├── 搜索和筛选
│   └── 操作按钮
└── Tabs支持
    ├── 主标签页
    └── 可添加更多标签页
```

## MCP工具调用参数

```javascript
{
  title: "商品管理",
  icon: "ShoppingCartOutlined",
  template: "table",
  collectionName: "products",
  enableTabs: true,
  hidden: false
}
```

## 错误处理

脚本包含完整的错误处理机制：

1. **集合不存在**: 如果products集合不存在，会显示警告并继续创建
2. **模板失败**: 如果table模板失败，会尝试使用blank模板
3. **连接失败**: 如果MCP服务器连接失败，会显示详细的错误信息
4. **权限问题**: 如果API token无效，会提示检查权限

## 后续操作

页面创建完成后，您可以：

1. **查看页面**: 在NocoBase管理界面中查看"商品管理"页面
2. **添加数据**: 在页面中添加商品数据
3. **自定义页面**: 根据需要调整页面布局和功能
4. **添加更多标签**: 使用Tabs功能添加更多子页面

## 技术细节

- **页面UID**: 自动生成唯一标识符
- **Schema**: 自动创建页面schema
- **路由**: 自动创建菜单路由
- **权限**: 继承默认权限设置
- **响应式**: 支持响应式布局

## 故障排除

### 常见问题

1. **页面显示空白**
   - 检查products集合是否存在
   - 确认集合中有数据

2. **创建失败**
   - 检查MCP服务器是否正常运行
   - 确认API token是否有效
   - 检查网络连接

3. **权限错误**
   - 确认用户有创建页面的权限
   - 检查API token的权限范围

### 调试步骤

1. 检查MCP服务器日志
2. 验证API连接
3. 查看NocoBase错误日志
4. 确认集合存在性

## 相关文件

- `create-products-route.js`: 完整的创建脚本
- `create-route-simple.js`: 简化的创建脚本
- `test-products-collection.js`: 商品集合测试脚本
- `test-products-page.js`: 商品页面测试脚本

## 注意事项

1. **数据备份**: 在执行前建议备份重要数据
2. **测试环境**: 建议先在测试环境中验证
3. **权限管理**: 确保使用适当的权限级别
4. **性能考虑**: 大量数据时注意页面性能