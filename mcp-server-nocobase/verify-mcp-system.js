#!/usr/bin/env node

/**
 * 验证通过 MCP 创建的学生选课系统
 */

import { spawn } from 'child_process';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// 简单的 MCP 客户端实现
class SimpleMCPClient {
  constructor() {
    this.serverProcess = null;
    this.requestId = 1;
  }

  async startServer() {
    console.log('🚀 启动 MCP 服务器...');
    
    this.serverProcess = spawn('node', [
      path.join(__dirname, 'dist', 'index.js'),
      '--base-url', 'https://app.dev.orb.local/api',
      '--token', 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VySWQiOjEsInJvbGVOYW1lIjoiYWRtaW4iLCJpYXQiOjE3NTQ1MjY4NDAsImV4cCI6MzMzMTIxMjY4NDB9.jub9QcFtDdjFrn3UzrT6PxNA19ZuHWGqIHfcBjdNhQA',
      '--app', 'mcp_playground'
    ], {
      stdio: ['pipe', 'pipe', 'pipe']
    });

    // 等待服务器启动
    await new Promise(resolve => setTimeout(resolve, 2000));
    console.log('✅ MCP 服务器已启动');
  }

  async sendRequest(method, params = {}) {
    return new Promise((resolve, reject) => {
      if (!this.serverProcess) {
        reject(new Error('Server not started'));
        return;
      }

      const request = {
        jsonrpc: '2.0',
        id: this.requestId++,
        method: method,
        params: params
      };

      let responseData = '';
      let errorData = '';

      const timeout = setTimeout(() => {
        reject(new Error('Request timeout'));
      }, 30000);

      const onData = (data) => {
        responseData += data.toString();
        
        // 尝试解析响应
        const lines = responseData.trim().split('\n');
        for (const line of lines) {
          if (line.trim().startsWith('{')) {
            try {
              const response = JSON.parse(line);
              if (response.id === request.id) {
                clearTimeout(timeout);
                this.serverProcess.stdout.off('data', onData);
                this.serverProcess.stderr.off('data', onError);
                
                if (response.error) {
                  reject(new Error(response.error.message || 'MCP Error'));
                } else {
                  resolve(response.result);
                }
                return;
              }
            } catch (e) {
              // 继续等待完整的响应
            }
          }
        }
      };

      const onError = (data) => {
        errorData += data.toString();
      };

      this.serverProcess.stdout.on('data', onData);
      this.serverProcess.stderr.on('data', onError);

      // 发送请求
      this.serverProcess.stdin.write(JSON.stringify(request) + '\n');
    });
  }

  async callTool(toolName, args = {}) {
    return this.sendRequest('tools/call', {
      name: toolName,
      arguments: args
    });
  }

  async close() {
    if (this.serverProcess) {
      this.serverProcess.kill();
      this.serverProcess = null;
    }
  }
}

// 验证函数
async function verifyMCPSystem() {
  const client = new SimpleMCPClient();
  
  try {
    await client.startServer();
    
    // 初始化连接
    await client.sendRequest('initialize', {
      protocolVersion: '2024-11-05',
      capabilities: {},
      clientInfo: { name: 'verify-client', version: '1.0.0' }
    });
    
    console.log('\n📊 === 验证 MCP 创建的学生选课系统 ===\n');
    
    // 1. 列出所有集合
    console.log('📋 1. 列出所有集合:');
    const collections = await client.callTool('list_collections');
    console.log(collections.content[0].text);
    
    // 2. 查看教师数据
    console.log('\n👨‍🏫 2. 教师数据:');
    const teachers = await client.callTool('list_records', {
      collection: 'mcp_teachers'
    });
    console.log(teachers.content[0].text);
    
    // 3. 查看课程数据
    console.log('\n📚 3. 课程数据:');
    const courses = await client.callTool('list_records', {
      collection: 'mcp_courses'
    });
    console.log(courses.content[0].text);
    
    // 4. 查看学生数据
    console.log('\n👨‍🎓 4. 学生数据:');
    const students = await client.callTool('list_records', {
      collection: 'mcp_students'
    });
    console.log(students.content[0].text);
    
    // 5. 查看集合结构
    console.log('\n🏗️  5. 学生集合结构:');
    const studentSchema = await client.callTool('get_collection_schema', {
      name: 'mcp_students'
    });
    console.log(studentSchema.content[0].text);
    
    console.log('\n✅ 验证完成！');
    
  } catch (error) {
    console.error('❌ 验证失败:', error.message);
  } finally {
    await client.close();
    console.log('\n🔚 MCP 服务器已关闭');
  }
}

// 运行验证
if (import.meta.url === `file://${process.argv[1]}`) {
  verifyMCPSystem().catch(console.error);
}
