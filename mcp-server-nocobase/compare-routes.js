#!/usr/bin/env node

/**
 * 对比 courses 路由和商品路由的差别
 */

import { spawn } from 'child_process';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// 简单的 MCP 客户端实现
class SimpleMCPClient {
  constructor() {
    this.serverProcess = null;
    this.requestId = 1;
  }

  async startServer() {
    console.log('🚀 启动 MCP 服务器...');
    
    this.serverProcess = spawn('node', [
      path.join(__dirname, 'dist', 'index.js'),
      '--base-url', 'https://app.dev.orb.local/api',
      '--token', 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VySWQiOjEsInJvbGVOYW1lIjoiYWRtaW4iLCJpYXQiOjE3NTQ1MjY4NDAsImV4cCI6MzMzMTIxMjY4NDB9.jub9QcFtDdjFrn3UzrT6PxNA19ZuHWGqIHfcBjdNhQA',
      '--app', 'mcp_playground'
    ], {
      stdio: ['pipe', 'pipe', 'pipe']
    });

    // 等待服务器启动
    await new Promise(resolve => setTimeout(resolve, 2000));
    console.log('✅ MCP 服务器已启动');
  }

  async sendRequest(method, params = {}) {
    return new Promise((resolve, reject) => {
      if (!this.serverProcess) {
        reject(new Error('Server not started'));
        return;
      }

      const request = {
        jsonrpc: '2.0',
        id: this.requestId++,
        method: method,
        params: params
      };

      let responseData = '';
      const timeout = setTimeout(() => {
        reject(new Error('Request timeout'));
      }, 30000);

      const onData = (data) => {
        responseData += data.toString();
        
        const lines = responseData.trim().split('\n');
        for (const line of lines) {
          if (line.trim().startsWith('{')) {
            try {
              const response = JSON.parse(line);
              if (response.id === request.id) {
                clearTimeout(timeout);
                this.serverProcess.stdout.off('data', onData);
                
                if (response.error) {
                  reject(new Error(response.error.message || 'MCP Error'));
                } else {
                  resolve(response.result);
                }
                return;
              }
            } catch (e) {
              // 继续等待完整的响应
            }
          }
        }
      };

      this.serverProcess.stdout.on('data', onData);
      this.serverProcess.stdin.write(JSON.stringify(request) + '\n');
    });
  }

  async callTool(toolName, args = {}) {
    return this.sendRequest('tools/call', {
      name: toolName,
      arguments: args
    });
  }

  async close() {
    if (this.serverProcess) {
      this.serverProcess.kill();
      this.serverProcess = null;
    }
  }
}

// 对比路由
async function compareRoutes() {
  const client = new SimpleMCPClient();
  
  try {
    await client.startServer();
    
    // 初始化连接
    await client.sendRequest('initialize', {
      protocolVersion: '2024-11-05',
      capabilities: {},
      clientInfo: { name: 'route-compare-client', version: '1.0.0' }
    });
    
    console.log('\n🔍 === 对比 courses 路由和商品路由 ===\n');
    
    // 步骤1: 获取所有路由
    console.log('📋 步骤1: 获取所有路由详细信息');
    let routesData = null;
    try {
      const routesResult = await client.callTool('list_routes');
      const routesText = routesResult.content[0].text;
      
      // 解析 JSON 数据
      const jsonMatch = routesText.match(/\[([\s\S]*)\]/);
      if (jsonMatch) {
        routesData = JSON.parse(jsonMatch[0]);
        console.log('✅ 成功获取路由数据');
      }
    } catch (error) {
      console.log('❌ 获取路由失败:', error.message);
      return;
    }
    
    // 步骤2: 分析 courses 路由
    console.log('\n📚 步骤2: 分析 courses 路由（正常工作）');
    const coursesRoute = routesData.find(route => route.title === 'courses');
    if (coursesRoute) {
      console.log('✅ courses 路由结构:');
      console.log(JSON.stringify(coursesRoute, null, 2));
    } else {
      console.log('❌ 未找到 courses 路由');
    }
    
    // 步骤3: 分析商品路由
    console.log('\n🛍️  步骤3: 分析商品路由（有问题）');
    const productRoutes = routesData.filter(route => 
      route.title === '商品管理' || route.title === '商品展示页'
    );
    
    if (productRoutes.length > 0) {
      console.log(`✅ 找到 ${productRoutes.length} 个商品路由:`);
      productRoutes.forEach((route, index) => {
        console.log(`\n--- 商品路由 ${index + 1}: ${route.title} ---`);
        console.log(JSON.stringify(route, null, 2));
      });
    } else {
      console.log('❌ 未找到商品路由');
    }
    
    // 步骤4: 对比关键差异
    console.log('\n🔍 步骤4: 关键差异分析');
    
    if (coursesRoute && productRoutes.length > 0) {
      const productRoute = productRoutes[0]; // 取第一个商品路由进行对比
      
      console.log('\n=== 关键字段对比 ===');
      
      const compareFields = [
        'type',
        'schemaUid', 
        'menuSchemaUid',
        'tabSchemaName',
        'enableTabs',
        'enableHeader',
        'hidden',
        'children'
      ];
      
      compareFields.forEach(field => {
        const coursesValue = coursesRoute[field];
        const productValue = productRoute[field];
        
        console.log(`\n${field}:`);
        console.log(`  courses: ${JSON.stringify(coursesValue)}`);
        console.log(`  商品路由: ${JSON.stringify(productValue)}`);
        
        if (JSON.stringify(coursesValue) !== JSON.stringify(productValue)) {
          console.log(`  ⚠️  差异发现！`);
        } else {
          console.log(`  ✅ 相同`);
        }
      });
    }
    
    // 步骤5: 检查页面 Schema 差异
    console.log('\n🏗️  步骤5: 检查页面 Schema 差异');
    
    if (coursesRoute) {
      console.log('\n--- courses 页面 Schema ---');
      try {
        const coursesSchema = await client.callTool('get_page_schema', {
          schemaUid: coursesRoute.schemaUid
        });
        console.log('✅ courses Schema:');
        console.log(coursesSchema.content[0].text);
      } catch (error) {
        console.log('❌ 获取 courses Schema 失败:', error.message);
      }
    }
    
    if (productRoutes.length > 0) {
      console.log('\n--- 商品页面 Schema ---');
      try {
        const productSchema = await client.callTool('get_page_schema', {
          schemaUid: productRoutes[0].schemaUid
        });
        console.log('✅ 商品页面 Schema:');
        console.log(productSchema.content[0].text);
      } catch (error) {
        console.log('❌ 获取商品页面 Schema 失败:', error.message);
      }
    }
    
    // 步骤6: 检查子路由差异
    console.log('\n👶 步骤6: 检查子路由差异');
    
    if (coursesRoute && coursesRoute.children && coursesRoute.children.length > 0) {
      console.log('\n✅ courses 有子路由:');
      coursesRoute.children.forEach((child, index) => {
        console.log(`  子路由 ${index + 1}:`, JSON.stringify(child, null, 2));
      });
    } else {
      console.log('\n❌ courses 没有子路由');
    }
    
    productRoutes.forEach((route, index) => {
      if (route.children && route.children.length > 0) {
        console.log(`\n✅ 商品路由 ${index + 1} 有子路由:`, route.children);
      } else {
        console.log(`\n❌ 商品路由 ${index + 1} 没有子路由`);
      }
    });
    
    console.log('\n📝 分析总结:');
    console.log('1. 对比了 courses 路由和商品路由的结构');
    console.log('2. 检查了关键字段的差异');
    console.log('3. 分析了页面 Schema 的不同');
    console.log('4. 查看了子路由的配置');
    
    console.log('\n💡 可能的问题:');
    console.log('- 商品路由可能缺少必要的子路由或标签页配置');
    console.log('- 页面 Schema 可能不完整');
    console.log('- 路由类型或配置可能有误');
    
  } catch (error) {
    console.error('❌ 对比失败:', error.message);
  } finally {
    await client.close();
    console.log('\n🔚 MCP 服务器已关闭');
  }
}

// 运行对比
if (import.meta.url === `file://${process.argv[1]}`) {
  compareRoutes().catch(console.error);
}
