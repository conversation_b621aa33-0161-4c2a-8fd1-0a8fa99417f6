#!/usr/bin/env node

/**
 * 直接调用MCP工具创建商品页面路由
 * 使用NocoBase MCP的create_page_route工具
 */

const { createProductsPageRoute } = require('./create-products-route');

// 直接执行创建函数
console.log('🚀 开始创建商品页面路由...\n');

createProductsPageRoute()
  .then((result) => {
    console.log('\n✅ 商品页面路由创建完成！');
    console.log('📍 请在NocoBase管理界面中查看"商品管理"页面');
  })
  .catch((error) => {
    console.error('\n❌ 创建失败:', error.message);
    process.exit(1);
  });