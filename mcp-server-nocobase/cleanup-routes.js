#!/usr/bin/env node

/**
 * 清理路由 - 删除除了 courses 以外的所有路由
 */

import { spawn } from 'child_process';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// 简单的 MCP 客户端实现
class SimpleMCPClient {
  constructor() {
    this.serverProcess = null;
    this.requestId = 1;
  }

  async startServer() {
    console.log('🚀 启动 MCP 服务器...');
    
    this.serverProcess = spawn('node', [
      path.join(__dirname, 'dist', 'index.js'),
      '--base-url', 'https://app.dev.orb.local/api',
      '--token', 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VySWQiOjEsInJvbGVOYW1lIjoiYWRtaW4iLCJpYXQiOjE3NTQ1MjY4NDAsImV4cCI6MzMzMTIxMjY4NDB9.jub9QcFtDdjFrn3UzrT6PxNA19ZuHWGqIHfcBjdNhQA',
      '--app', 'mcp_playground'
    ], {
      stdio: ['pipe', 'pipe', 'pipe']
    });

    // 等待服务器启动
    await new Promise(resolve => setTimeout(resolve, 2000));
    console.log('✅ MCP 服务器已启动');
  }

  async sendRequest(method, params = {}) {
    return new Promise((resolve, reject) => {
      if (!this.serverProcess) {
        reject(new Error('Server not started'));
        return;
      }

      const request = {
        jsonrpc: '2.0',
        id: this.requestId++,
        method: method,
        params: params
      };

      let responseData = '';
      const timeout = setTimeout(() => {
        reject(new Error('Request timeout'));
      }, 30000);

      const onData = (data) => {
        responseData += data.toString();
        
        const lines = responseData.trim().split('\n');
        for (const line of lines) {
          if (line.trim().startsWith('{')) {
            try {
              const response = JSON.parse(line);
              if (response.id === request.id) {
                clearTimeout(timeout);
                this.serverProcess.stdout.off('data', onData);
                
                if (response.error) {
                  reject(new Error(response.error.message || 'MCP Error'));
                } else {
                  resolve(response.result);
                }
                return;
              }
            } catch (e) {
              // 继续等待完整的响应
            }
          }
        }
      };

      this.serverProcess.stdout.on('data', onData);
      this.serverProcess.stdin.write(JSON.stringify(request) + '\n');
    });
  }

  async callTool(toolName, args = {}) {
    return this.sendRequest('tools/call', {
      name: toolName,
      arguments: args
    });
  }

  async close() {
    if (this.serverProcess) {
      this.serverProcess.kill();
      this.serverProcess = null;
    }
  }
}

// 清理路由的主函数
async function cleanupRoutes() {
  const client = new SimpleMCPClient();
  
  try {
    await client.startServer();
    
    // 初始化连接
    await client.sendRequest('initialize', {
      protocolVersion: '2024-11-05',
      capabilities: {},
      clientInfo: { name: 'cleanup-routes', version: '1.0.0' }
    });
    
    console.log('\n🧹 === 清理路由（保留 courses）===\n');
    
    // 步骤1: 获取当前所有路由
    console.log('📋 步骤1: 获取当前所有路由');
    let allRoutes = [];
    
    try {
      const routesResult = await client.callTool('list_routes');
      const routesText = routesResult.content[0].text;
      
      // 解析路由数据
      const jsonMatch = routesText.match(/\[([\s\S]*)\]/);
      if (jsonMatch) {
        allRoutes = JSON.parse(jsonMatch[0]);
        console.log(`✅ 找到 ${allRoutes.length} 个路由`);
        
        // 显示所有路由
        console.log('\n📋 当前路由列表:');
        allRoutes.forEach((route, index) => {
          console.log(`   ${index + 1}. ID: ${route.id}, 标题: "${route.title}", 类型: ${route.type}`);
        });
      } else {
        console.log('❌ 无法解析路由数据');
        return;
      }
    } catch (error) {
      console.log('❌ 获取路由列表失败:', error.message);
      return;
    }
    
    // 步骤2: 识别需要删除的路由
    console.log('\n🔍 步骤2: 识别需要删除的路由');
    
    const routesToDelete = [];
    const routesToKeep = [];
    
    allRoutes.forEach(route => {
      if (route.title === 'courses') {
        routesToKeep.push(route);
        console.log(`✅ 保留: ID ${route.id} - "${route.title}"`);
      } else {
        routesToDelete.push(route);
        console.log(`🗑️  删除: ID ${route.id} - "${route.title}"`);
      }
    });
    
    console.log(`\n📊 统计:`);
    console.log(`   保留路由: ${routesToKeep.length} 个`);
    console.log(`   删除路由: ${routesToDelete.length} 个`);
    
    if (routesToDelete.length === 0) {
      console.log('\n🎉 没有需要删除的路由！');
      return;
    }
    
    // 步骤3: 删除路由
    console.log('\n🗑️  步骤3: 开始删除路由');
    
    let deletedCount = 0;
    let failedCount = 0;
    
    // 按 ID 倒序删除（先删除子路由，再删除父路由）
    const sortedRoutesToDelete = routesToDelete.sort((a, b) => b.id - a.id);
    
    for (const route of sortedRoutesToDelete) {
      try {
        console.log(`🗑️  删除路由: ID ${route.id} - "${route.title}"`);
        
        await client.callTool('delete_route', {
          id: route.id
        });
        
        deletedCount++;
        console.log(`✅ 成功删除: ID ${route.id}`);
        
        // 添加延迟避免请求过快
        await new Promise(resolve => setTimeout(resolve, 500));
        
      } catch (error) {
        failedCount++;
        console.log(`❌ 删除失败: ID ${route.id} - ${error.message}`);
      }
    }
    
    // 步骤4: 验证删除结果
    console.log('\n📋 步骤4: 验证删除结果');
    
    try {
      const finalRoutesResult = await client.callTool('list_routes');
      const finalRoutesText = finalRoutesResult.content[0].text;
      
      const finalJsonMatch = finalRoutesText.match(/\[([\s\S]*)\]/);
      if (finalJsonMatch) {
        const finalRoutes = JSON.parse(finalJsonMatch[0]);
        
        console.log(`\n📊 最终结果:`);
        console.log(`   剩余路由: ${finalRoutes.length} 个`);
        console.log(`   成功删除: ${deletedCount} 个`);
        console.log(`   删除失败: ${failedCount} 个`);
        
        console.log('\n📋 剩余路由列表:');
        finalRoutes.forEach((route, index) => {
          console.log(`   ${index + 1}. ID: ${route.id}, 标题: "${route.title}", 类型: ${route.type}`);
        });
        
        // 检查是否只剩下 courses
        const nonCoursesRoutes = finalRoutes.filter(route => route.title !== 'courses');
        if (nonCoursesRoutes.length === 0) {
          console.log('\n🎉 清理完成！现在只剩下 courses 路由了！');
        } else {
          console.log('\n⚠️  还有一些非 courses 路由未被删除:');
          nonCoursesRoutes.forEach(route => {
            console.log(`   - ID: ${route.id}, 标题: "${route.title}"`);
          });
        }
      }
    } catch (error) {
      console.log('❌ 验证删除结果失败:', error.message);
    }
    
    console.log('\n🎯 清理操作完成！');
    
  } catch (error) {
    console.error('❌ 清理失败:', error.message);
  } finally {
    await client.close();
    console.log('\n🔚 MCP 服务器已关闭');
  }
}

// 运行清理
if (import.meta.url === `file://${process.argv[1]}`) {
  cleanupRoutes().catch(console.error);
}
