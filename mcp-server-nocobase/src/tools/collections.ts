import { McpServer } from "@modelcontextprotocol/sdk/server/mcp.js";
import { z } from "zod";
import { NocoBaseClient } from "../client.js";

export async function registerCollectionTools(server: <PERSON>cp<PERSON>erver, client: NocoBaseClient) {
  // List all collections
  server.registerTool(
    "list_collections",
    {
      title: "List Collections",
      description: "List all collections in the NocoBase application",
      inputSchema: {
        includeMeta: z.boolean().optional().describe("Include detailed metadata for each collection")
      }
    },
    async ({ includeMeta = false }) => {
      try {
        const collections = includeMeta 
          ? await client.listCollectionsMeta()
          : await client.listCollections();

        return {
          content: [{
            type: "text",
            text: `Found ${collections.length} collections:\n\n${collections.map(c => 
              `• ${c.name} (${c.title || 'No title'})\n  ${c.description || 'No description'}\n  Fields: ${c.fields?.length || 'Unknown'}`
            ).join('\n\n')}`
          }]
        };
      } catch (error) {
        return {
          content: [{
            type: "text",
            text: `Error listing collections: ${error instanceof Error ? error.message : String(error)}`
          }],
          isError: true
        };
      }
    }
  );

  // Get collection details
  server.registerTool(
    "get_collection",
    {
      title: "Get Collection Details",
      description: "Get detailed information about a specific collection",
      inputSchema: {
        name: z.string().describe("Name of the collection to retrieve")
      }
    },
    async ({ name }) => {
      try {
        const collection = await client.getCollection(name);

        const details = [
          `Collection: ${collection.name}`,
          `Title: ${collection.title || 'No title'}`,
          `Description: ${collection.description || 'No description'}`,
          `Auto-generated ID: ${collection.autoGenId ? 'Yes' : 'No'}`,
          `Timestamps: ${collection.createdAt ? 'Created' : ''}${collection.updatedAt ? (collection.createdAt ? ', Updated' : 'Updated') : ''}`,
          `User tracking: ${collection.createdBy ? 'Created by' : ''}${collection.updatedBy ? (collection.createdBy ? ', Updated by' : 'Updated by') : ''}`,
          `Hidden: ${collection.hidden ? 'Yes' : 'No'}`,
          `Inherits: ${collection.inherit ? 'Yes' : 'No'}`,
          '',
          `Fields (${collection.fields?.length || 0}):`
        ];

        if (collection.fields && collection.fields.length > 0) {
          collection.fields.forEach(field => {
            details.push(`  • ${field.name} (${field.type}) - ${field.interface || 'No interface'}`);
            if (field.description) {
              details.push(`    ${field.description}`);
            }
          });
        } else {
          details.push('  No fields found');
        }

        return {
          content: [{
            type: "text",
            text: details.join('\n')
          }]
        };
      } catch (error) {
        return {
          content: [{
            type: "text",
            text: `Error getting collection '${name}': ${error instanceof Error ? error.message : String(error)}`
          }],
          isError: true
        };
      }
    }
  );

  // Create collection
  server.registerTool(
    "create_collection",
    {
      title: "Create Collection",
      description: "Create a new collection in NocoBase",
      inputSchema: {
        name: z.string().describe("Name of the collection (must be unique)"),
        title: z.string().optional().describe("Display title for the collection"),
        description: z.string().optional().describe("Description of the collection"),
        autoGenId: z.boolean().optional().default(true).describe("Auto-generate ID field"),
        createdAt: z.boolean().optional().default(true).describe("Add created timestamp field"),
        updatedAt: z.boolean().optional().default(true).describe("Add updated timestamp field"),
        createdBy: z.boolean().optional().default(false).describe("Add created by user field"),
        updatedBy: z.boolean().optional().default(false).describe("Add updated by user field"),
        fields: z.array(z.object({
          name: z.string(),
          type: z.string(),
          interface: z.string().optional(),
          uiSchema: z.any().optional()
        })).optional().describe("Initial fields to create")
      }
    },
    async (params) => {
      try {
        // 准备默认字段定义
        const defaultFields: any[] = [];

        // ID 字段
        if (params.autoGenId !== false) {
          defaultFields.push({
            name: 'id',
            type: 'bigInt',
            interface: 'id',
            autoIncrement: true,
            primaryKey: true,
            allowNull: false,
            uiSchema: {
              type: 'number',
              title: '{{t("ID")}}',
              'x-component': 'InputNumber',
              'x-read-pretty': true
            }
          });
        }

        // 创建时间字段
        if (params.createdAt !== false) {
          defaultFields.push({
            name: 'createdAt',
            type: 'date',
            interface: 'createdAt',
            field: 'createdAt',
            uiSchema: {
              type: 'datetime',
              title: '{{t("Created at")}}',
              'x-component': 'DatePicker',
              'x-component-props': {},
              'x-read-pretty': true
            }
          });
        }

        // 更新时间字段
        if (params.updatedAt !== false) {
          defaultFields.push({
            name: 'updatedAt',
            type: 'date',
            interface: 'updatedAt',
            field: 'updatedAt',
            uiSchema: {
              type: 'datetime',
              title: '{{t("Last updated at")}}',
              'x-component': 'DatePicker',
              'x-component-props': {},
              'x-read-pretty': true
            }
          });
        }

        // 创建人字段
        if (params.createdBy === true) {
          defaultFields.push({
            name: 'createdBy',
            type: 'belongsTo',
            interface: 'createdBy',
            target: 'users',
            foreignKey: 'createdById',
            targetKey: 'id',
            uiSchema: {
              type: 'object',
              title: '{{t("Created by")}}',
              'x-component': 'AssociationField',
              'x-component-props': {
                fieldNames: {
                  value: 'id',
                  label: 'nickname'
                }
              },
              'x-read-pretty': true
            }
          });
        }

        // 更新人字段
        if (params.updatedBy === true) {
          defaultFields.push({
            name: 'updatedBy',
            type: 'belongsTo',
            interface: 'updatedBy',
            target: 'users',
            foreignKey: 'updatedById',
            targetKey: 'id',
            uiSchema: {
              type: 'object',
              title: '{{t("Last updated by")}}',
              'x-component': 'AssociationField',
              'x-component-props': {
                fieldNames: {
                  value: 'id',
                  label: 'nickname'
                }
              },
              'x-read-pretty': true
            }
          });
        }

        // 合并用户提供的字段和默认字段
        const allFields = [...defaultFields, ...(params.fields || [])];

        // 准备集合参数
        const collectionParams = {
          ...params,
          fields: allFields
        };

        // Filter out undefined values
        const cleanParams = Object.fromEntries(
          Object.entries(collectionParams).filter(([_, value]) => value !== undefined)
        );

        const collection = await client.createCollection(cleanParams);

        const defaultFieldsCount = defaultFields.length;
        const userFieldsCount = (params.fields || []).length;

        return {
          content: [{
            type: "text",
            text: `Successfully created collection '${collection.name}' (${collection.title || 'No title'}) with ${defaultFieldsCount} default fields and ${userFieldsCount} custom fields`
          }]
        };
      } catch (error) {
        return {
          content: [{
            type: "text",
            text: `Error creating collection '${params.name}': ${error instanceof Error ? error.message : String(error)}`
          }],
          isError: true
        };
      }
    }
  );

  // Update collection
  server.registerTool(
    "update_collection",
    {
      title: "Update Collection",
      description: "Update an existing collection's configuration",
      inputSchema: {
        name: z.string().describe("Name of the collection to update"),
        title: z.string().optional().describe("New display title"),
        description: z.string().optional().describe("New description"),
        hidden: z.boolean().optional().describe("Hide/show the collection")
      }
    },
    async ({ name, ...updates }) => {
      try {
        // Filter out undefined values
        const cleanUpdates = Object.fromEntries(
          Object.entries(updates).filter(([_, value]) => value !== undefined)
        );
        const collection = await client.updateCollection(name, cleanUpdates);

        return {
          content: [{
            type: "text",
            text: `Successfully updated collection '${collection.name}'`
          }]
        };
      } catch (error) {
        return {
          content: [{
            type: "text",
            text: `Error updating collection '${name}': ${error instanceof Error ? error.message : String(error)}`
          }],
          isError: true
        };
      }
    }
  );

  // Delete collection
  server.registerTool(
    "delete_collection",
    {
      title: "Delete Collection",
      description: "Delete a collection and all its data (use with caution!)",
      inputSchema: {
        name: z.string().describe("Name of the collection to delete"),
        confirm: z.boolean().describe("Confirmation that you want to delete the collection and all its data")
      }
    },
    async ({ name, confirm }) => {
      if (!confirm) {
        return {
          content: [{
            type: "text",
            text: "Collection deletion cancelled. Set 'confirm' to true to proceed with deletion."
          }]
        };
      }

      try {
        await client.deleteCollection(name);

        return {
          content: [{
            type: "text",
            text: `Successfully deleted collection '${name}' and all its data`
          }]
        };
      } catch (error) {
        return {
          content: [{
            type: "text",
            text: `Error deleting collection '${name}': ${error instanceof Error ? error.message : String(error)}`
          }],
          isError: true
        };
      }
    }
  );
}
