#!/usr/bin/env node

/**
 * 创建测试路由用于调试
 */

import { spawn } from 'child_process';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// 简单的 MCP 客户端实现
class SimpleMCPClient {
  constructor() {
    this.serverProcess = null;
    this.requestId = 1;
  }

  async startServer() {
    console.log('🚀 启动 MCP 服务器...');
    
    this.serverProcess = spawn('node', [
      path.join(__dirname, 'dist', 'index.js'),
      '--base-url', 'https://app.dev.orb.local/api',
      '--token', 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VySWQiOjEsInJvbGVOYW1lIjoiYWRtaW4iLCJpYXQiOjE3NTQ1MjY4NDAsImV4cCI6MzMzMTIxMjY4NDB9.jub9QcFtDdjFrn3UzrT6PxNA19ZuHWGqIHfcBjdNhQA',
      '--app', 'mcp_playground'
    ], {
      stdio: ['pipe', 'pipe', 'pipe']
    });

    // 等待服务器启动
    await new Promise(resolve => setTimeout(resolve, 2000));
    console.log('✅ MCP 服务器已启动');
  }

  async sendRequest(method, params = {}) {
    return new Promise((resolve, reject) => {
      if (!this.serverProcess) {
        reject(new Error('Server not started'));
        return;
      }

      const request = {
        jsonrpc: '2.0',
        id: this.requestId++,
        method: method,
        params: params
      };

      let responseData = '';
      const timeout = setTimeout(() => {
        reject(new Error('Request timeout'));
      }, 30000);

      const onData = (data) => {
        responseData += data.toString();
        
        const lines = responseData.trim().split('\n');
        for (const line of lines) {
          if (line.trim().startsWith('{')) {
            try {
              const response = JSON.parse(line);
              if (response.id === request.id) {
                clearTimeout(timeout);
                this.serverProcess.stdout.off('data', onData);
                
                if (response.error) {
                  reject(new Error(response.error.message || 'MCP Error'));
                } else {
                  resolve(response.result);
                }
                return;
              }
            } catch (e) {
              // 继续等待完整的响应
            }
          }
        }
      };

      this.serverProcess.stdout.on('data', onData);
      this.serverProcess.stdin.write(JSON.stringify(request) + '\n');
    });
  }

  async callTool(toolName, args = {}) {
    return this.sendRequest('tools/call', {
      name: toolName,
      arguments: args
    });
  }

  async close() {
    if (this.serverProcess) {
      this.serverProcess.kill();
      this.serverProcess = null;
    }
  }
}

// 创建测试路由
async function createTestRoute() {
  const client = new SimpleMCPClient();
  
  try {
    await client.startServer();
    
    // 初始化连接
    await client.sendRequest('initialize', {
      protocolVersion: '2024-11-05',
      capabilities: {},
      clientInfo: { name: 'test-route-client', version: '1.0.0' }
    });
    
    console.log('\n🧪 === 创建测试路由用于调试 ===\n');
    
    // 步骤1: 创建测试路由
    console.log('📝 步骤1: 创建测试路由');
    try {
      const testRouteResult = await client.callTool('create_page_route', {
        title: '测试路由调试',
        icon: 'BugOutlined',
        template: 'table',
        collectionName: 'products',
        enableTabs: true,
        hidden: false
      });
      console.log('✅ 测试路由创建成功');
      console.log(testRouteResult.content[0].text);
      
      // 解析结果获取路由ID
      const resultText = testRouteResult.content[0].text;
      const routeMatch = resultText.match(/"id":\s*(\d+)/);
      const routeId = routeMatch ? parseInt(routeMatch[1]) : null;
      
      if (routeId) {
        console.log(`\n🔍 路由ID: ${routeId}`);
        
        // 步骤2: 获取新建路由的详细信息
        console.log('\n📋 步骤2: 获取新建路由的详细信息');
        try {
          const routeDetailResult = await client.callTool('get_route', { id: routeId });
          console.log('✅ 新建路由详情获取成功');
          console.log(routeDetailResult.content[0].text);
        } catch (error) {
          console.log('❌ 获取新建路由详情失败:', error.message);
        }
        
        // 步骤3: 获取courses路由的详细信息进行对比
        console.log('\n📚 步骤3: 获取courses路由的详细信息进行对比');
        try {
          const coursesResult = await client.callTool('get_route', { id: 5 }); // 假设courses路由ID为5
          console.log('✅ courses路由详情获取成功');
          console.log(coursesResult.content[0].text);
        } catch (error) {
          console.log('❌ 获取courses路由详情失败:', error.message);
          
          // 尝试查找courses路由
          console.log('\n🔍 尝试查找courses路由...');
          try {
            const routesResult = await client.callTool('list_routes', { tree: true });
            const routesText = routesResult.content[0].text;
            console.log('✅ 路由列表获取成功');
            
            // 查找courses路由
            const lines = routesText.split('\n');
            for (const line of lines) {
              if (line.includes('courses') || line.includes('Courses')) {
                console.log(`找到courses相关路由: ${line}`);
              }
            }
          } catch (listError) {
            console.log('❌ 查找courses路由失败:', listError.message);
          }
        }
        
        // 步骤4: 尝试通过schema API获取更多信息
        console.log('\n🔧 步骤4: 通过schema API获取详细信息');
        try {
          // 这里需要根据实际的schema API来调用
          // 暂时先记录路由ID用于后续分析
          console.log(`💡 新建路由ID: ${routeId}`);
          console.log('💡 请手动通过schema API获取详细信息进行对比');
        } catch (error) {
          console.log('❌ schema API调用失败:', error.message);
        }
        
      } else {
        console.log('❌ 无法解析路由ID');
      }
      
    } catch (error) {
      console.log('❌ 创建测试路由失败:', error.message);
    }
    
    console.log('\n🎉 测试路由创建完成！');
    console.log('\n📝 下一步:');
    console.log('1. 手动通过schema API获取新建路由的详细信息');
    console.log('2. 获取courses路由的详细信息');
    console.log('3. 对比两者差异');
    console.log('4. 在源码中查找问题原因');
    
  } catch (error) {
    console.error('❌ 创建测试路由失败:', error.message);
  } finally {
    await client.close();
    console.log('\n🔚 MCP 服务器已关闭');
  }
}

// 运行脚本
if (import.meta.url === `file://${process.argv[1]}`) {
  createTestRoute().catch(console.error);
}