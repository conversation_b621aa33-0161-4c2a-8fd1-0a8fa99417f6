#!/usr/bin/env node

/**
 * 创建商品页面路由 - 使用MCP工具调用方式
 */

import { spawn } from 'child_process';
import path from 'path';
import { fileURLToPath } from 'url';
import { getRouteConfig, getFallbackConfigs } from './config/products-route-config.js';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// 简单的 MCP 客户端实现
class SimpleMCPClient {
  constructor() {
    this.serverProcess = null;
    this.requestId = 1;
  }

  async startServer() {
    console.log('🚀 启动 MCP 服务器...');
    
    this.serverProcess = spawn('node', [
      path.join(__dirname, 'dist', 'index.js'),
      '--base-url', 'https://app.dev.orb.local/api',
      '--token', 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VySWQiOjEsInJvbGVOYW1lIjoiYWRtaW4iLCJpYXQiOjE3NTQ1MjY4NDAsImV4cCI6MzMzMTIxMjY4NDB9.jub9QcFtDdjFrn3UzrT6PxNA19ZuHWGqIHfcBjdNhQA',
      '--app', 'mcp_playground'
    ], {
      stdio: ['pipe', 'pipe', 'pipe']
    });

    // 等待服务器启动
    await new Promise(resolve => setTimeout(resolve, 2000));
    console.log('✅ MCP 服务器已启动');
  }

  async sendRequest(method, params = {}) {
    return new Promise((resolve, reject) => {
      if (!this.serverProcess) {
        reject(new Error('Server not started'));
        return;
      }

      const request = {
        jsonrpc: '2.0',
        id: this.requestId++,
        method: method,
        params: params
      };

      let responseData = '';
      const timeout = setTimeout(() => {
        reject(new Error('Request timeout'));
      }, 30000);

      const onData = (data) => {
        responseData += data.toString();
        
        const lines = responseData.trim().split('\n');
        for (const line of lines) {
          if (line.trim().startsWith('{')) {
            try {
              const response = JSON.parse(line);
              if (response.id === request.id) {
                clearTimeout(timeout);
                this.serverProcess.stdout.off('data', onData);
                
                if (response.error) {
                  reject(new Error(response.error.message || 'MCP Error'));
                } else {
                  resolve(response.result);
                }
                return;
              }
            } catch (e) {
              // 继续等待完整的响应
            }
          }
        }
      };

      this.serverProcess.stdout.on('data', onData);
      this.serverProcess.stdin.write(JSON.stringify(request) + '\n');
    });
  }

  async callTool(toolName, args = {}) {
    return this.sendRequest('tools/call', {
      name: toolName,
      arguments: args
    });
  }

  async close() {
    if (this.serverProcess) {
      this.serverProcess.kill();
      this.serverProcess = null;
    }
  }
}

// 创建商品页面路由
async function createProductsPageRoute() {
  const client = new SimpleMCPClient();
  
  try {
    await client.startServer();
    
    // 初始化连接
    await client.sendRequest('initialize', {
      protocolVersion: '2024-11-05',
      capabilities: {},
      clientInfo: { name: 'products-route-client', version: '1.0.0' }
    });
    
    console.log('\n🛍️ === 创建商品页面路由 ===\n');
    
    // 步骤1: 查看现有集合
    console.log('🔍 步骤1: 查看现有集合');
    let productCollection = 'products'; // 默认集合名称
    
    try {
      const collections = await client.callTool('list_collections', {
        includeMeta: true
      });
      console.log('✅ 集合列表:');
      console.log(collections.content[0].text);
      
      // 尝试找到products集合或类似的商品集合
      const collectionsText = collections.content[0].text;
      if (collectionsText.includes('products')) {
        console.log('✅ 找到products集合');
      } else if (collectionsText.includes('商品')) {
        console.log('✅ 找到商品相关集合');
        // 可以在这里添加逻辑来找到具体的商品集合名称
      } else {
        console.log('⚠️  未找到products集合，将尝试使用默认名称');
      }
    } catch (error) {
      console.log('❌ 查看集合失败:', error.message);
      console.log('⚠️  将继续使用默认集合名称 "products"');
    }
    
    // 步骤2: 创建商品页面路由
    console.log('\n🛣️  步骤2: 创建商品页面路由');
    
    // 获取路由配置
    const routeConfig = getRouteConfig({ collectionName: productCollection });
    console.log('📋 路由配置:', JSON.stringify(routeConfig, null, 2));
    
    try {
      const routeResult = await client.callTool('create_page_route', routeConfig);
      
      console.log('✅ 商品页面路由创建成功！');
      console.log('📋 路由创建结果:');
      console.log(routeResult.content[0].text);
      
      // 提取页面UID
      const routeText = routeResult.content[0].text;
      const schemaUidMatch = routeText.match(/"schemaUid":\s*"([^"]+)"/);
      const pageUid = schemaUidMatch ? schemaUidMatch[1] : '未知';
      
      console.log('\n📄 页面信息:');
      console.log(`   标题: 商品管理`);
      console.log(`   图标: ShoppingCartOutlined`);
      console.log(`   模板: table`);
      console.log(`   集合: ${productCollection}`);
      console.log(`   Tabs: 启用`);
      console.log(`   页面UID: ${pageUid}`);
      
      return routeResult;
      
    } catch (error) {
      console.log('❌ 创建路由失败:', error.message);
      
      // 如果主配置失败，尝试使用备选配置
      console.log('\n🔄 尝试使用备选配置创建路由...');
      const fallbackConfigs = getFallbackConfigs();
      
      for (const fallbackConfig of fallbackConfigs) {
        console.log(`🔄 尝试配置: ${fallbackConfig.reason}`);
        try {
          const fallbackResult = await client.callTool('create_page_route', fallbackConfig);
        
        console.log('✅ 使用备选配置创建路由成功！');
        console.log('📋 路由创建结果:');
        console.log(fallbackResult.content[0].text);
        
        console.log('\n💡 提示: 页面已使用备选配置创建');
        console.log(`💡 原因: ${fallbackConfig.reason}`);
        
        return fallbackResult;
        
      } catch (fallbackError) {
        console.log(`❌ 备选配置失败: ${fallbackError.message}`);
        // 继续尝试下一个备选配置
      }
    }
    
    // 如果所有备选配置都失败，抛出原始错误
    throw error;
  }
    
  } catch (error) {
    console.error('❌ 创建商品页面路由失败:', error.message);
    throw error;
  } finally {
    await client.close();
    console.log('\n🔚 MCP 服务器已关闭');
  }
}

// 运行创建
async function main() {
  try {
    const result = await createProductsPageRoute();
    
    console.log('\n🎉 === 创建完成 ===');
    console.log('\n📝 总结:');
    console.log('✅ 成功创建商品管理页面路由');
    console.log('✅ 使用了table模板展示商品集合数据');
    console.log('✅ 设置了合适的图标和标题');
    console.log('✅ 启用了tabs功能');
    console.log('✅ 路由配置完成');
    
    console.log('\n🔗 后续操作:');
    console.log('💡 请在 NocoBase 管理界面中查看新创建的"商品管理"页面');
    console.log('💡 如果页面显示空白，请确保products集合存在');
    console.log('💡 如果集合不存在，请先创建products集合');
    console.log('💡 页面支持tabs功能，可以添加多个子页面');
    
    console.log('\n🛠️  技术细节:');
    console.log('• 页面模板: table (自动包含数据表格)');
    console.log('• 数据集合: products');
    console.log('• 图标: ShoppingCartOutlined');
    console.log('• Tabs功能: 已启用');
    console.log('• 页面类型: 页面路由');
    
  } catch (error) {
    console.error('\n❌ 执行失败:', error.message);
    process.exit(1);
  }
}

// 运行主函数
if (import.meta.url === `file://${process.argv[1]}`) {
  main().catch(console.error);
}

export { createProductsPageRoute };