#!/usr/bin/env node

/**
 * 通过 MCP 创建完整的学生选课系统测试
 * 演示如何创建集合、字段和插入数据
 */

const { spawn } = require('child_process');
const path = require('path');

// 使用记忆中的开发环境配置
const config = {
  baseUrl: 'https://app.dev.orb.local/api',
  token: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VySWQiOjEsInJvbGVOYW1lIjoiYWRtaW4iLCJpYXQiOjE3NTQ1MjY4NDAsImV4cCI6MzMzMTIxMjY4NDB9.jub9QcFtDdjFrn3UzrT6PxNA19ZuHWGqIHfcBjdNhQA',
  app: 'mcp_playground'
};

// 测试步骤定义
const testSteps = [
  // === 第一阶段：创建基础集合 ===
  {
    name: '创建学生集合',
    tool: 'create_collection',
    params: {
      name: 'demo_students',
      title: '演示学生',
      autoGenId: true,
      createdAt: true,
      updatedAt: true
    }
  },
  {
    name: '创建课程集合',
    tool: 'create_collection',
    params: {
      name: 'demo_courses',
      title: '演示课程',
      autoGenId: true,
      createdAt: true,
      updatedAt: true
    }
  },
  {
    name: '创建教师集合',
    tool: 'create_collection',
    params: {
      name: 'demo_teachers',
      title: '演示教师',
      autoGenId: true,
      createdAt: true,
      updatedAt: true
    }
  },

  // === 第二阶段：添加基础字段 ===
  {
    name: '学生-添加姓名字段',
    tool: 'create_field',
    params: {
      collection: 'demo_students',
      name: 'name',
      type: 'string',
      interface: 'input',
      uiSchema: {
        title: '学生姓名',
        'x-component': 'Input',
        required: true
      }
    }
  },
  {
    name: '学生-添加学号字段',
    tool: 'create_field',
    params: {
      collection: 'demo_students',
      name: 'student_id',
      type: 'string',
      interface: 'input',
      uiSchema: {
        title: '学号',
        'x-component': 'Input'
      }
    }
  },
  {
    name: '课程-添加课程名称字段',
    tool: 'create_field',
    params: {
      collection: 'demo_courses',
      name: 'name',
      type: 'string',
      interface: 'input',
      uiSchema: {
        title: '课程名称',
        'x-component': 'Input',
        required: true
      }
    }
  },
  {
    name: '课程-添加学分字段',
    tool: 'create_field',
    params: {
      collection: 'demo_courses',
      name: 'credits',
      type: 'integer',
      interface: 'number',
      uiSchema: {
        title: '学分',
        'x-component': 'InputNumber'
      }
    }
  },
  {
    name: '教师-添加姓名字段',
    tool: 'create_field',
    params: {
      collection: 'demo_teachers',
      name: 'name',
      type: 'string',
      interface: 'input',
      uiSchema: {
        title: '教师姓名',
        'x-component': 'Input',
        required: true
      }
    }
  },

  // === 第三阶段：创建关联字段 ===
  {
    name: '课程-添加教师关联字段(belongsTo)',
    tool: 'create_field',
    params: {
      collection: 'demo_courses',
      name: 'teacher',
      type: 'belongsTo',
      interface: 'm2o',
      target: 'demo_teachers',
      foreignKey: 'teacherId',
      targetKey: 'id',
      uiSchema: {
        title: '授课教师',
        'x-component': 'AssociationField',
        'x-component-props': {
          multiple: false,
          fieldNames: {
            label: 'name',
            value: 'id'
          }
        }
      }
    }
  },
  {
    name: '学生-添加课程关联字段(belongsTo)',
    tool: 'create_field',
    params: {
      collection: 'demo_students',
      name: 'course',
      type: 'belongsTo',
      interface: 'm2o',
      target: 'demo_courses',
      foreignKey: 'courseId',
      targetKey: 'id',
      uiSchema: {
        title: '选修课程',
        'x-component': 'AssociationField',
        'x-component-props': {
          multiple: false,
          fieldNames: {
            label: 'name',
            value: 'id'
          }
        }
      }
    }
  },
  {
    name: '教师-添加课程关联字段(hasMany)',
    tool: 'create_field',
    params: {
      collection: 'demo_teachers',
      name: 'courses',
      type: 'hasMany',
      interface: 'o2m',
      target: 'demo_courses',
      sourceKey: 'id',
      foreignKey: 'teacherId',
      uiSchema: {
        title: '教授课程',
        'x-component': 'AssociationField',
        'x-component-props': {
          multiple: true,
          fieldNames: {
            label: 'name',
            value: 'id'
          }
        }
      }
    }
  },
  {
    name: '课程-添加学生关联字段(hasMany)',
    tool: 'create_field',
    params: {
      collection: 'demo_courses',
      name: 'students',
      type: 'hasMany',
      interface: 'o2m',
      target: 'demo_students',
      sourceKey: 'id',
      foreignKey: 'courseId',
      uiSchema: {
        title: '选课学生',
        'x-component': 'AssociationField',
        'x-component-props': {
          multiple: true,
          fieldNames: {
            label: 'name',
            value: 'id'
          }
        }
      }
    }
  }
];

// 数据插入步骤
const dataSteps = [
  // 创建教师数据
  {
    name: '创建教师-张教授',
    tool: 'create_record',
    params: {
      collection: 'demo_teachers',
      data: {
        name: '张教授'
      }
    }
  },
  {
    name: '创建教师-李老师',
    tool: 'create_record',
    params: {
      collection: 'demo_teachers',
      data: {
        name: '李老师'
      }
    }
  },
  // 创建课程数据
  {
    name: '创建课程-高等数学',
    tool: 'create_record',
    params: {
      collection: 'demo_courses',
      data: {
        name: '高等数学',
        credits: 4,
        teacherId: 1  // 假设张教授的ID是1
      }
    }
  },
  {
    name: '创建课程-线性代数',
    tool: 'create_record',
    params: {
      collection: 'demo_courses',
      data: {
        name: '线性代数',
        credits: 3,
        teacherId: 2  // 假设李老师的ID是2
      }
    }
  },
  // 创建学生数据
  {
    name: '创建学生-王小明',
    tool: 'create_record',
    params: {
      collection: 'demo_students',
      data: {
        name: '王小明',
        student_id: 'S001',
        courseId: 1  // 选修高等数学
      }
    }
  },
  {
    name: '创建学生-李小红',
    tool: 'create_record',
    params: {
      collection: 'demo_students',
      data: {
        name: '李小红',
        student_id: 'S002',
        courseId: 1  // 选修高等数学
      }
    }
  },
  {
    name: '创建学生-张小华',
    tool: 'create_record',
    params: {
      collection: 'demo_students',
      data: {
        name: '张小华',
        student_id: 'S003',
        courseId: 2  // 选修线性代数
      }
    }
  }
];

// 执行MCP工具调用
function callMCPTool(tool, params) {
  return new Promise((resolve, reject) => {
    const mcpPath = path.join(__dirname, 'dist', 'index.js');
    
    const child = spawn('node', [
      mcpPath,
      '--base-url', config.baseUrl,
      '--token', config.token,
      '--app', config.app
    ], {
      stdio: ['pipe', 'pipe', 'pipe']
    });

    let stdout = '';
    let stderr = '';

    child.stdout.on('data', (data) => {
      stdout += data.toString();
    });

    child.stderr.on('data', (data) => {
      stderr += data.toString();
    });

    child.on('close', (code) => {
      if (code === 0) {
        resolve({ stdout, stderr });
      } else {
        reject(new Error(`Process exited with code ${code}: ${stderr}`));
      }
    });

    // 发送MCP请求
    const request = {
      jsonrpc: '2.0',
      id: 1,
      method: 'tools/call',
      params: {
        name: tool,
        arguments: params
      }
    };

    child.stdin.write(JSON.stringify(request) + '\n');
    child.stdin.end();
  });
}

// 主测试函数
async function runTest() {
  console.log('🚀 开始创建学生选课系统演示...\n');
  console.log(`📍 连接到: ${config.baseUrl}`);
  console.log(`🏷️  应用: ${config.app}\n`);

  // 第一阶段：创建集合和字段
  console.log('=== 第一阶段：创建集合和字段 ===\n');
  
  for (const step of testSteps) {
    try {
      console.log(`⏳ ${step.name}...`);
      const result = await callMCPTool(step.tool, step.params);
      console.log(`✅ ${step.name} - 成功`);
    } catch (error) {
      console.log(`❌ ${step.name} - 失败: ${error.message}`);
    }
  }

  console.log('\n=== 第二阶段：插入测试数据 ===\n');
  
  // 第二阶段：插入数据
  for (const step of dataSteps) {
    try {
      console.log(`⏳ ${step.name}...`);
      const result = await callMCPTool(step.tool, step.params);
      console.log(`✅ ${step.name} - 成功`);
    } catch (error) {
      console.log(`❌ ${step.name} - 失败: ${error.message}`);
    }
  }

  console.log('\n🎉 学生选课系统创建完成！');
  console.log('\n📊 系统包含：');
  console.log('• 3个集合：demo_students, demo_courses, demo_teachers');
  console.log('• 多个字段：姓名、学号、课程名称、学分等');
  console.log('• 关联关系：学生-课程(belongsTo), 教师-课程(hasMany), 课程-学生(hasMany)');
  console.log('• 测试数据：2名教师、2门课程、3名学生');
}

// 运行测试
if (require.main === module) {
  runTest().catch(console.error);
}

module.exports = { runTest, testSteps, dataSteps };
