# 商品页面路由创建完整指南

## 概述

本指南介绍如何使用NocoBase MCP工具创建一个完整的商品页面路由，包含所有必要的配置和脚本。

## 功能特性

✅ **完整的商品管理页面**
- 页面标题: "商品管理"
- 图标: ShoppingCartOutlined
- 模板: table (自动生成数据表格)
- 集合: products
- Tabs支持: 启用

✅ **MCP工具集成**
- 使用`create_page_route`工具
- 自动处理集合检查
- 支持多种备选配置
- 完整的错误处理

✅ **自动化流程**
- 自动启动MCP服务器
- 自动检查集合存在性
- 自动创建页面路由
- 自动生成页面结构

## 文件结构

```
mcp-server-nocobase/
├── create-products-route.js          # 主要创建脚本
├── create-route-simple.js            # 简化创建脚本
├── config/
│   └── products-route-config.js      # 路由配置文件
├── CREATE_PRODUCTS_ROUTE.md          # 详细说明文档
└── test-products-page.js             # 测试脚本
```

## 快速开始

### 1. 构建MCP服务器

```bash
cd /Users/<USER>/dev/mcp-nocobase/mcp-server-nocobase
npm run build
```

### 2. 运行创建脚本

```bash
# 使用完整脚本
node create-products-route.js

# 或使用简化脚本
node create-route-simple.js
```

### 3. 验证结果

在NocoBase管理界面中查看新创建的"商品管理"页面。

## 详细配置

### 路由配置参数

```javascript
{
  title: '商品管理',
  icon: 'ShoppingCartOutlined',
  template: 'table',
  collectionName: 'products',
  enableTabs: true,
  hidden: false
}
```

### 备选配置

如果主配置失败，脚本会自动尝试以下备选配置：

1. **blank模板**: 如果table模板不可用
2. **英文标题**: 如果中文标题有问题
3. **禁用Tabs**: 如果Tabs功能有问题

### 预期字段

脚本期望products集合包含以下字段：

```javascript
[
  'id', 'name', 'price', 'description',
  'category', 'stock', 'status',
  'createdAt', 'updatedAt', 'createdBy', 'updatedBy'
]
```

## 执行流程

1. **启动MCP服务器**
   - 加载NocoBase MCP服务器
   - 建立API连接
   - 验证访问权限

2. **检查集合**
   - 列出所有可用集合
   - 查找products集合
   - 记录集合信息

3. **创建路由**
   - 使用create_page_route工具
   - 应用配置参数
   - 生成页面schema

4. **处理结果**
   - 显示创建结果
   - 提取页面UID
   - 记录页面信息

5. **错误处理**
   - 主配置失败时尝试备选
   - 提供详细的错误信息
   - 建议解决方案

## 使用场景

### 场景1: 全新安装
- 首次使用NocoBase
- 需要创建商品管理功能
- 自动化页面创建

### 场景2: 现有系统
- 已有products集合
- 需要添加管理页面
- 快速部署功能

### 场景3: 测试环境
- 需要测试页面创建功能
- 验证MCP工具工作
- 调试页面配置

## 故障排除

### 常见问题

1. **页面显示空白**
   ```
   解决方案: 确保products集合存在且包含数据
   ```

2. **创建失败**
   ```
   解决方案: 检查MCP服务器状态和API权限
   ```

3. **权限错误**
   ```
   解决方案: 验证API token和用户权限
   ```

### 调试步骤

1. 检查服务器日志
2. 验证API连接
3. 确认集合存在
4. 测试工具调用

## 扩展功能

### 自定义配置

修改`config/products-route-config.js`文件来自定义配置：

```javascript
export const CUSTOM_CONFIG = {
  title: '自定义商品管理',
  icon: 'CustomIcon',
  template: 'table',
  // ... 其他配置
};
```

### 添加更多字段

在products集合中添加更多字段：

```javascript
const additionalFields = [
  { name: 'brand', type: 'string', title: '品牌' },
  { name: 'weight', type: 'decimal', title: '重量' },
  // ... 更多字段
];
```

### 集成其他功能

- 商品图片上传
- 库存管理
- 订单关联
- 统计报表

## 最佳实践

### 1. 环境准备
- 确保NocoBase服务器正常运行
- 准备有效的API token
- 备份重要数据

### 2. 配置管理
- 使用配置文件管理参数
- 实施版本控制
- 文档化配置变更

### 3. 错误处理
- 实现完整的错误处理
- 提供详细的日志信息
- 建立监控机制

### 4. 测试验证
- 在测试环境中验证
- 执行完整的功能测试
- 检查用户体验

## 相关资源

### 文档
- [NocoBase官方文档](https://docs.nocobase.com/)
- [MCP协议文档](https://modelcontextprotocol.io/)
- [API参考手册](./docs/)

### 工具
- NocoBase MCP服务器
- create_page_route工具
- list_collections工具
- 其他MCP工具

### 示例
- test-products-collection.js
- test-products-page.js
- 其他测试脚本

## 维护和支持

### 版本控制
- 使用Git管理代码
- 标记重要版本
- 记录变更历史

### 问题报告
- 提供详细的错误信息
- 包含复现步骤
- 附加相关日志

### 功能请求
- 描述新功能需求
- 提供使用场景
- 讨论实现方案

---

*本指南涵盖了使用NocoBase MCP工具创建商品页面路由的完整流程。如有问题，请参考相关文档或联系技术支持。*