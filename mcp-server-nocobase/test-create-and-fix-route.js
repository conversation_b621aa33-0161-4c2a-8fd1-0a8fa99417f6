#!/usr/bin/env node

/**
 * 测试创建路由后立即修复页面结构的方法
 * 基于 fix-page-structure.js 的发现
 */

import { spawn } from 'child_process';
import path from 'path';
import { fileURLToPath } from 'url';
import axios from 'axios';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// 测试配置
const testConfig = {
  baseUrl: 'https://app.dev.orb.local/api',
  token: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VySWQiOjEsInJvbGVOYW1lIjoiYWRtaW4iLCJpYXQiOjE3NTQ1MjY4NDAsImV4cCI6MzMzMTIxMjY4NDB9.jub9QcFtDdjFrn3UzrT6PxNA19ZuHWGqIHfcBjdNhQA',
  app: 'mcp_playground'
};

// 简单的 MCP 客户端实现
class SimpleMCPClient {
  constructor() {
    this.serverProcess = null;
    this.requestId = 1;
  }

  async startServer() {
    console.log('🚀 启动 MCP 服务器...');
    
    this.serverProcess = spawn('node', [
      path.join(__dirname, 'dist', 'index.js'),
      '--base-url', testConfig.baseUrl,
      '--token', testConfig.token,
      '--app', testConfig.app
    ], {
      stdio: ['pipe', 'pipe', 'pipe']
    });

    // 等待服务器启动
    await new Promise(resolve => setTimeout(resolve, 2000));
    console.log('✅ MCP 服务器已启动');
  }

  async sendRequest(method, params = {}) {
    return new Promise((resolve, reject) => {
      if (!this.serverProcess) {
        reject(new Error('Server not started'));
        return;
      }

      const request = {
        jsonrpc: '2.0',
        id: this.requestId++,
        method: method,
        params: params
      };

      let responseData = '';
      const timeout = setTimeout(() => {
        reject(new Error('Request timeout'));
      }, 30000);

      const onData = (data) => {
        responseData += data.toString();
        
        const lines = responseData.trim().split('\n');
        for (const line of lines) {
          if (line.trim().startsWith('{')) {
            try {
              const response = JSON.parse(line);
              if (response.id === request.id) {
                clearTimeout(timeout);
                this.serverProcess.stdout.off('data', onData);
                
                if (response.error) {
                  reject(new Error(response.error.message || 'MCP Error'));
                } else {
                  resolve(response.result);
                }
                return;
              }
            } catch (e) {
              // 继续等待完整的响应
            }
          }
        }
      };

      this.serverProcess.stdout.on('data', onData);
      this.serverProcess.stdin.write(JSON.stringify(request) + '\n');
    });
  }

  async callTool(toolName, args = {}) {
    return this.sendRequest('tools/call', {
      name: toolName,
      arguments: args
    });
  }

  async close() {
    if (this.serverProcess) {
      this.serverProcess.kill();
      this.serverProcess = null;
    }
  }
}

// 修复页面结构的函数（基于 fix-page-structure.js）
async function fixPageStructure(pageUid) {
  console.log(`\n🔧 修复页面结构: ${pageUid}`);
  
  const client = axios.create({
    baseURL: testConfig.baseUrl,
    headers: {
      'Authorization': `Bearer ${testConfig.token}`,
      'X-App': testConfig.app,
      'Content-Type': 'application/json'
    }
  });

  try {
    // 1. 获取当前页面状态
    console.log('📋 1. 获取当前页面状态');
    const currentResponse = await client.get(`/uiSchemas:getJsonSchema/${pageUid}`);
    const currentSchema = currentResponse.data.data;
    
    console.log('📊 当前页面结构:');
    console.log(JSON.stringify(currentSchema, null, 2));
    
    // 2. 创建正确的简单页面结构（基于 fix-page-structure.js）
    console.log('\n📋 2. 创建正确的简单页面结构');
    
    const correctPageStructure = {
      type: 'void',
      'x-component': 'Page',
      name: currentSchema.name, // 保持原有的 name
      'x-uid': pageUid,
      'x-async': false
    };
    
    console.log('📊 正确的页面结构:');
    console.log(JSON.stringify(correctPageStructure, null, 2));
    
    // 3. 使用 patch 方法更新页面结构
    console.log('\n📋 3. 更新页面结构');
    
    const patchResponse = await client.post('/uiSchemas:patch', {
      'x-uid': pageUid,
      ...correctPageStructure
    });
    
    console.log('✅ 页面结构更新成功!');
    console.log('📥 响应:', JSON.stringify(patchResponse.data, null, 2));
    
    // 4. 验证更新结果
    console.log('\n📋 4. 验证更新结果');
    
    const verifyResponse = await client.get(`/uiSchemas:getJsonSchema/${pageUid}`);
    const verifiedSchema = verifyResponse.data.data;
    
    console.log('📊 更新后的页面结构:');
    console.log(JSON.stringify(verifiedSchema, null, 2));
    
    // 检查结构是否正确
    const isCorrectStructure = (
      verifiedSchema.type === 'void' &&
      verifiedSchema['x-component'] === 'Page' &&
      verifiedSchema['x-uid'] === pageUid &&
      !verifiedSchema.schema // 不应该有嵌套的 schema 属性
    );
    
    console.log('\n🔍 结构验证:');
    console.log(`   - 类型正确: ${verifiedSchema.type === 'void'}`);
    console.log(`   - 组件正确: ${verifiedSchema['x-component'] === 'Page'}`);
    console.log(`   - UID正确: ${verifiedSchema['x-uid'] === pageUid}`);
    console.log(`   - 无嵌套schema: ${!verifiedSchema.schema}`);
    console.log(`   - 整体结构正确: ${isCorrectStructure}`);
    
    return isCorrectStructure;
    
  } catch (error) {
    console.log('❌ 修复页面结构失败:', error.response?.status, error.response?.data || error.message);
    return false;
  }
}

// 测试创建和修复路由
async function testCreateAndFixRoute() {
  const client = new SimpleMCPClient();
  
  try {
    await client.startServer();
    
    // 初始化连接
    await client.sendRequest('initialize', {
      protocolVersion: '2024-11-05',
      capabilities: {},
      clientInfo: { name: 'create-and-fix-test', version: '1.0.0' }
    });
    
    console.log('\n🏗️  === 测试创建和修复路由 ===\n');
    
    // 步骤1: 创建一个简单的页面路由（不包含复杂结构）
    console.log('📄 步骤1: 创建简单的页面路由');
    let routeResult = null;
    let pageUid = null;
    
    try {
      // 使用最简单的配置创建路由
      routeResult = await client.callTool('create_page_route', {
        title: '商品管理（创建后修复）',
        icon: 'ShoppingOutlined',
        template: 'blank'  // 使用最简单的模板
      });
      console.log('✅ 简单路由创建成功');
      console.log(routeResult.content[0].text);
      
      // 提取页面 UID
      const routeText = routeResult.content[0].text;
      const pageUidMatch = routeText.match(/Page UID: ([a-zA-Z0-9-]+)/);
      if (pageUidMatch) {
        pageUid = pageUidMatch[1];
        console.log(`✅ 提取到页面 UID: ${pageUid}`);
      } else {
        throw new Error('无法提取页面 UID');
      }
      
    } catch (error) {
      console.log('❌ 创建路由失败:', error.message);
      return;
    }
    
    // 步骤2: 立即修复页面结构
    console.log('\n🔧 步骤2: 修复页面结构');
    
    const fixResult = await fixPageStructure(pageUid);
    
    if (fixResult) {
      console.log('🎉 页面结构修复成功！');
    } else {
      console.log('❌ 页面结构修复失败');
    }
    
    // 步骤3: 验证最终结果
    console.log('\n📋 步骤3: 验证最终结果');
    
    console.log('\n🎯 测试完成！');
    console.log('\n📝 总结:');
    console.log('✅ 创建了简单的页面路由');
    console.log('✅ 使用 PATCH 方法修复了页面结构');
    console.log('✅ 应用了 fix-page-structure.js 的修复策略');
    
    if (pageUid) {
      console.log(`\n🔗 测试访问链接:`);
      console.log(`https://app.dev.orb.local/apps/mcp_playground/admin/${pageUid}`);
      console.log('\n💡 现在应该可以看到 "Add block" 按钮了！');
    }
    
  } catch (error) {
    console.error('❌ 测试失败:', error.message);
  } finally {
    await client.close();
    console.log('\n🔚 MCP 服务器已关闭');
  }
}

// 运行测试
if (import.meta.url === `file://${process.argv[1]}`) {
  testCreateAndFixRoute().catch(console.error);
}
